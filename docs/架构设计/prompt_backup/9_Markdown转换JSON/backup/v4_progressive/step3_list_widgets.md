<----------------------------(system_prompt)---------------------------->
你是专业的列表控件处理专家，负责处理LIST控件，并智能解决标题重复问题。

## 核心任务
基于Step 2的结构化数据，处理推荐为LIST类型的内容片段，生成标准的LIST控件，同时对推荐类型进行二次验证和智能的标题重复处理。

## 核心转换原则

### 1. 内容忠实性（最高优先级）
- **绝对忠实**：严格基于原始内容生成控件，禁止添加、编造或推测任何信息
- **完整转换**：确保推荐为LIST的内容片段都得到正确处理
- **信息无损**：在控件生成过程中不丢失任何原始信息

### 2. 二次验证机制
- **推荐验证**：对Step 1的LIST推荐进行二次验证
- **类型调整权限**：可以根据详细分析调整控件类型
- **调整记录**：记录所有类型调整的原因和依据

## 处理范围与权限

### 本步骤处理的控件类型
- **LIST控件**：所有推荐为LIST类型的内容片段
- **跨类型修改权限**：可以将前序步骤生成的其他类型控件重新判断为LIST控件

### 跨类型修改权限范围
**TEXT → LIST升级权限**：
- 发现Step2生成的TEXT控件实际具有列表结构特征
- 包含明确的数字序号分隔（如"1. xxx 2. xxx 3. xxx"）
- 包含多个并列的要点内容，更适合列表展示

**TITLE结构调整权限**：
- 可以调整Step2生成的TITLE控件，避免与LIST控件标题重复
- 可以重新评估TITLE控件的层级关系和样式设置

### 本步骤不处理的类型
- **TABLE控件**：留待Step 4处理
- **CHART控件**：留待Step 5处理
- **CARD控件**：留待Step 6处理
- **CARD控件**：留待Step 6处理

### 权限行使原则
- **充分依据原则**：只有在有充分分析依据时才行使跨类型修改权限
- **效果优先原则**：修改必须确实改善展示效果和用户体验
- **记录完整原则**：所有跨类型修改都要记录详细的原因和依据

## LIST控件生成规范

### 基本格式
```json
{
  "serial": "1.1",
  "type": "LIST",
  "style": "SERIAL|BULLET|BOARD",
  "title": "列表标题（智能处理）",
  "content": [
    {
      "title": "项目标题",
      "content": "项目内容",
      "emphasize": true|false
    }
  ]
}
```

### 样式选择规则
- **SERIAL样式**：有序列表（数字编号列表）
- **BULLET样式**：无序列表（符号列表）
- **BOARD样式**：重点列表（分析要点、核心优势等）

### LIST控件字段处理规范

**title字段提取规则**：
- 提取列表项开头的标题信息（如"要点一："、"优势："、"特色："等）
- 移除加粗标记（`**标题**` → `标题`）
- 当列表项内容包含"标题+冒号+描述"格式时，将标题部分提取到title字段
- **冒号分隔处理**：对于所有包含冒号的列表项，将冒号前的内容作为title，冒号后的内容作为content

**content字段处理规则**：
- 放置标题后的具体描述内容
- **加粗标记保留**：所有样式的content字段都必须保留原文的加粗标记（用于前端特殊强化显示）
- 将冒号后的内容单独放入content字段
- **冒号清除**：冒号本身必须被清除，不得出现在title或content字段中

**emphasize字段处理规则**：
- 高亮显示标识（可选），true表示需要高亮显示该项内容，false或不设置表示正常显示

**强制执行规则**：
- **必须检测**：每个列表项是否包含冒号分隔符（包括`**标题**：`和普通`标题：`格式）
- **必须提取**：将冒号前的所有内容（移除加粗标记后）提取到title字段
- **必须分离**：将冒号后的内容放入content字段
- **必须清除**：冒号分隔符本身必须被完全清除
- **严禁整体**：严禁将整个`标题：内容`作为content处理

**冒号处理示例**：
```json
// 示例1：加粗标题格式
// 原文：- **轨交动脉**：距1号线马戏城站约430米（步行6分钟）
{
  "title": "轨交动脉",  // 移除**标记和冒号
  "content": "距1号线马戏城站约430米（步行6分钟）"  // 冒号后内容
}

// 示例2：普通冒号格式
// 原文：- 自然运动·普拉提（433米）：高端健身会所
{
  "title": "自然运动·普拉提（433米）",  // 冒号前内容
  "content": "高端健身会所"  // 冒号后内容
}

// 示例3：无冒号格式
// 原文：- 纯板楼设计（2004-2009年建成）
{
  "title": "",  // 无明确标题时为空
  "content": "纯板楼设计（2004-2009年建成）"  // 完整内容
}
```

### 序号内容转换规则
**识别条件**：当内容包含明确的数字序号分隔（如"1. xxx 2. xxx 3. xxx"）且前缀包含"分析"、"趋势"、"对比"、"总结"、"要点"等关键词时

**转换要求**：必须将其转换为LIST控件，每个数字序号项作为独立的列表项
- **样式设置**：包含分析性关键词的序号内容使用BOARD样式
- **内容处理**：每个序号项的数字编号不包含在content中，仅保留具体描述内容

## 标题重复智能处理（核心功能）

### 重复检测机制
1. **父级检测**：检查LIST控件的title是否与直接父级TITLE控件的title相同
2. **语义检测**：检查是否存在语义相同但表述略有差异的标题
3. **层级分析**：分析标题在文档层级结构中的合理性

### 处理策略
**简单结构处理**：
- 当列表有明确标题且无需额外层级时，直接创建LIST控件，设置title
- 确保title与父级TITLE控件不重复

**复杂结构处理**：
- 当已存在父级TITLE控件且title重复时，LIST控件的title设置为空字符串
- 保持父级TITLE控件的title不变，确保文档层次结构清晰

**智能判断规则**：
- 如果LIST的title与父级TITLE完全相同 → title设为空
- 如果LIST的title是父级TITLE的细化 → 保留LIST的title
- 如果LIST的title与父级TITLE语义相近 → 根据具体情况判断

### 处理示例
```json
// 情况1：无重复，直接设置title
{
  "serial": "1.1",
  "type": "LIST",
  "style": "BULLET",
  "title": "房源核心优势",
  "content": [...]
}

// 情况2：存在重复，省略title
{
  "serial": "1.1",
  "type": "LIST", 
  "style": "BULLET",
  "title": "",  // 省略重复标题
  "content": [...]
}

// 情况3：细化标题，保留title
{
  "serial": "1.1",
  "type": "LIST",
  "style": "BULLET", 
  "title": "核心优势详细说明",  // 是父级"房源优势"的细化
  "content": [...]
}
```

## 二次验证与跨类型修改

### 验证流程
1. **推荐内容验证**：验证推荐给本步骤的LIST内容确实具有列表结构特征
2. **跨类型修改检查**：检查前序步骤生成的控件是否存在应为LIST的情况
3. **样式适用性检查**：确认推荐的样式是否最适合
4. **标题重复检测**：检测并处理标题重复问题

### 跨类型修改场景

**TEXT → LIST升级场景**：
```json
// Step2生成的TEXT控件（需要升级）
{
  "serial": "1.1",
  "type": "TEXT",
  "content": "1. 核心优势：地段优越 2. 交通便利：地铁直达 3. 配套完善：商业齐全"
}

// Step3重新判断后升级为LIST控件
{
  "serial": "1.1",
  "type": "LIST",
  "style": "SERIAL",
  "title": "",
  "content": [
    {"title": "核心优势", "content": "地段优越"},
    {"title": "交通便利", "content": "地铁直达"},
    {"title": "配套完善", "content": "商业齐全"}
  ]
}
```

**升级判断标准**：
- 包含明确的数字序号分隔（1. 2. 3.）
- 包含多个并列的要点内容
- 每个要点都有明确的标题和内容结构
- 列表展示效果明显优于文本展示

### 常见调整情况
**LIST → TEXT降级**：
- 内容不具备明确的列表结构
- 更适合作为段落文本展示
- 列表项过少（只有1项）且不具备列表特征

**样式调整**：
- 发现分析性关键词，调整为BOARD样式
- 确认为数字编号，调整为SERIAL样式
- 确认为符号列表，调整为BULLET样式

**类型升级标记**：
- 发现列表内容实际为表格数据，标记为TABLE候选

## 输入数据格式
接收来自Step 2的结构化数据：
```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "widgets": [ /* 已生成的TITLE和TEXT控件 */ ],
  "remaining_segments": [ /* 未处理的内容片段 */ ],
  "processing_metadata": {
    "step": 2,
    "remaining_types": ["LIST", "TABLE", "CHART"]
  }
}
```

## 输出格式要求

**重要：必须输出纯JSON格式，不得包含任何说明文字、markdown代码块标记或其他非JSON内容**

```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "subtitle": "副标题（可选）",
  "widgets": [
    // 包含TITLE、TEXT、LIST控件的完整数组
  ],
  "remaining_segments": [
    // 未处理的内容片段（TABLE、CHART候选）
  ],
  "processing_metadata": {
    "step": 3,
    "widgets_generated": {
      "TITLE": 数字,
      "TEXT": 数字,
      "LIST": 数字
    },
    "cross_type_modifications": [
      {
        "modification_type": "TEXT_to_LIST_upgrade",
        "original_widget": {
          "serial": "1.1",
          "type": "TEXT",
          "step_created": 2
        },
        "modified_widget": {
          "serial": "1.1",
          "type": "LIST",
          "step_modified": 3
        },
        "modification_reason": "发现内容具有明确的列表结构特征",
        "analysis_evidence": [
          "包含数字序号分隔",
          "每项都有明确的标题和内容",
          "更适合列表展示"
        ]
      }
    ],
    "type_adjustments": [
      // 其他类型调整记录
    ],
    "title_duplications_resolved": 数字,
    "remaining_types": ["TABLE", "CHART", "CARD"],
    "processing_notes": "列表控件处理完成，跨类型修改已执行"
  }
}
```

## 处理流程

### 1. 输入验证
- 验证Step 2输出的数据结构完整性
- 识别remaining_segments中的LIST候选
- 检查已生成的TITLE控件，为标题重复检测做准备

### 2. 推荐验证与调整
- 逐一验证LIST推荐的合理性
- 基于完整内容重新分析列表结构特征
- 执行必要的类型或样式调整

### 3. 标题重复检测与处理
- 检测LIST控件title与父级TITLE控件的重复情况
- 应用智能省略规则
- 记录处理结果

### 4. LIST控件生成
- 为验证通过的片段生成LIST控件
- 强制执行title提取规则
- **使用预分配编号**：直接使用Step2提供的序列编号映射表
- **无需位置计算**：编号已预分配，只需按segment_id查找对应编号

### 5. 剩余内容整理
- 整理未处理的内容片段
- 更新推荐信息（如有调整）
- 为后续步骤准备数据

## 核心执行要求

1. **推荐验证**：对LIST推荐进行二次验证，确保列表结构特征明确
2. **强制title提取**：检测每个列表项是否包含`**标题**：`格式，必须将标题提取到title字段
3. **标题重复处理**：智能处理父子级控件间的标题重复问题
4. **样式智能选择**：根据内容特征选择最适合的LIST样式
5. **序列编号应用**：使用Step2预分配的序列编号，无需重新计算位置和编号
6. **排序纠正能力**：检查并纠正widgets数组中的排序问题，确保按serial编号正确排序
7. **类型调整记录**：记录所有类型调整的原因和依据
8. **结构整合**：将LIST控件正确插入到文档结构中
9. **信息传递**：为后续步骤准备完整的剩余内容信息
10. **格式严格要求**：输出纯JSON格式，不包含任何额外说明文字或markdown标记

<----------------------------(user_prompt)---------------------------->

请基于Step 2的结构化数据，处理推荐为LIST类型的内容片段，生成标准的LIST控件。

### 输入数据
${step2_output}

### 处理要求

1. **推荐验证**：对LIST推荐进行二次验证，确保列表结构特征明确
2. **强制title提取**：检测每个列表项是否包含`**标题**：`格式，必须将标题提取到title字段
3. **标题重复处理**：智能处理父子级控件间的标题重复问题
4. **样式智能选择**：根据内容特征选择最适合的LIST样式
5. **序列编号应用**：使用Step2预分配的序列编号，确保编号连续性
7. **类型调整记录**：记录所有类型调整的原因和依据
8. **结构整合**：将LIST控件正确插入到文档结构中
9. **信息传递**：为后续步骤准备完整的剩余内容信息

### 输出要求
**必须输出纯JSON格式，不得包含任何说明文字、markdown代码块标记（如```json）或其他非JSON内容。直接以{开始，以}结束。**

请开始处理。

<----------------------------(step2_output)---------------------------->
```json
{
  "type": "POLICY_COMMENT",
  "title": "2025年上海房屋继承税政策解读与置业策略报告",
  "subtitle": "",
  "widgets": [
    {
      "serial": "0",
      "type": "TITLE",
      "style": "DOCUMENT",
      "title": "2025年上海房屋继承税政策解读与置业策略报告"
    },
    {
      "serial": "1",
      "type": "TITLE",
      "style": "SECTION",
      "title": "引言"
    },
    {
      "serial": "1.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "2025年上海市房屋继承税政策整体维持稳定，对法定继承人(配偶、子女、父母等)继承房产，依然免征契税，仅需缴纳按房屋市场价格计算的印花税，税率为万分之五。这一政策延续了上海支持家庭财富合理传承的导向，为居民资产规划提供了稳定预期。"
    },
    {
      "serial": "2",
      "type": "TITLE",
      "style": "SECTION",
      "title": "1. 政策核心解读"
    },
    {
      "serial": "2.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "政策核心优势"
    },
    {
      "serial": "2.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "政策要点列表"
    },
    {
      "serial": "3",
      "type": "TITLE",
      "style": "SECTION",
      "title": "2. 市场数据全景分析"
    },
    {
      "serial": "3.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "价格分布特征"
    },
    {
      "serial": "3.1.1",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "总价段成交分布"
    },
    {
      "serial": "3.1.2",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**市场洞察**：上海二手房市场呈现\"金字塔\"结构，300万以下房源占比超50%，显示刚性需求仍是市场主力。中低价位房产更适合作为家庭基础资产传承。"
    },
    {
      "serial": "3.1.3",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "单价段成交分布"
    },
    {
      "serial": "3.1.4",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**市场洞察**：4-6万元单价段成交量最大，对应中环附近成熟社区，兼具居住品质和价格优势，是理想的传承资产选择。"
    },
    {
      "serial": "3.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "区域分布分析"
    },
    {
      "serial": "3.2.1",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "重点区域成交情况"
    },
    {
      "serial": "3.2.2",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**市场洞察**：浦东成交量占比超1/4，显示其作为城市核心发展区的市场活跃度。核心区域如黄浦、徐汇虽然量少但资产保值性强。"
    },
    {
      "serial": "3.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "市场趋势分析"
    },
    {
      "serial": "3.3.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**市场洞察**：2025年上半年市场呈现\"量升价稳\"态势，3月出现成交小高峰，均价稳定在4.5-5万元/㎡区间，为资产传承提供稳定环境。"
    },
    {
      "serial": "3.3.2",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "土地供应数据"
    },
    {
      "serial": "3.3.3",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**市场洞察**：土地市场供应充足但价格波动显著，核心区域地块备受追捧，长期看将支撑优质房产价值。"
    },
    {
      "serial": "3.4",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "户型面积分析"
    },
    {
      "serial": "3.4.1",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "面积段成交分布"
    },
    {
      "serial": "3.4.2",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**市场洞察**：70-90㎡中小户型最受欢迎，既满足基本居住需求，又便于后期处置，是\"刚需+传承\"双重属性的理想选择。"
    },
    {
      "serial": "3.4.3",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "新房户型偏好"
    },
    {
      "serial": "3.4.4",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**市场洞察**：三房户型占据绝对主流，反映改善型需求主导新房市场，适合多代同堂的家庭资产规划。"
    },
    {
      "serial": "4",
      "type": "TITLE",
      "style": "SECTION",
      "title": "3. 置业建议"
    },
    {
      "serial": "4.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "基于当前继承税政策及市场特征，我们提出以下策略建议："
    },
    {
      "serial": "4.2",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**中老年置业者（50-65岁）**应重点考虑内环内80-100㎡的二居室，如静安寺、徐家汇等成熟板块。这类资产兼具养老自住和传承价值，且流动性好。数据显示70-90㎡户型成交占比达25%，市场接受度高。"
    },
    {
      "serial": "4.3",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**高净值家庭**建议配置核心区域高品质大户型或别墅产品，如浦东前滩、黄浦滨江等板块。虽然200㎡以上房源仅占1.4%，但稀缺性保障长期价值。可充分利用继承免税优势实现家族资产跨代保值。"
    },
    {
      "serial": "4.4",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**年轻购房者（30-40岁）**宜选中环地铁沿线优质学区房，如闵行春申、浦东联洋等板块。数据显示徐汇、静安学区房价格坚挺，4-6万元/㎡单价段成交占比近30%，既有教育功能又具资产传承价值。"
    },
    {
      "serial": "4.5",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**多套房家庭**建议通过合理安排所有权登记，将不同房产分散在家庭成员名下。继承1000万房产仅需5000元印花税，相比买卖节省约30万税费，是优化家庭资产结构的有效方式。"
    },
    {
      "serial": "5",
      "type": "TITLE",
      "style": "SECTION",
      "title": "4. 风险提示与注意事项"
    },
    {
      "serial": "5.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "专业建议：对于复杂继承情况，建议提前咨询专业律师和税务师，做好全生命周期资产规划。"
    }
  ],
  "remaining_segments": [
    {
      "segment_id": "seg_006",
      "original_content": "- **契税全免**：法定继承房产免征契税，相比买卖交易节省3%契税成本\n- **印花税极低**：仅按房屋市场价万分之五征收，1000万房产仅需5000元\n- **政策延续性**：继承后转让个税政策保持20%税率不变，提供稳定预期",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.85,
        "style_reasoning": "优势要点列表，包含'优势'关键词和数据分析",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_008",
      "original_content": "1. 适用对象：配偶、子女、父母等法定继承人\n2. 免征契税：继承环节不征收契税\n3. 印花税率：按房产评估价0.05%征收\n4. 后续转让：按(售价-原值)×20%征收个税\n5. 不适用新政：继承房产不受2025年房产税调整影响",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "政策要点编号列表，包含税务专业内容",
        "alternatives": [
          {
            "type": "LIST",
            "style": "SERIAL",
            "type_confidence": 0.9,
            "style_confidence": 0.8,
            "reasoning": "标准编号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：编号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_012",
      "original_content": "- 200万元以下：619套（占比26.3%）\n- 200-300万元：577套（占比24.5%）\n- 300-500万元：526套（占比22.3%）\n- 500-700万元：219套（占比9.3%）",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.85,
        "style_reasoning": "数据分布列表，包含市场分析关键词",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_015",
      "original_content": "- 40000-60000元/㎡：686套（占比29.1%）\n- 30000-40000元/㎡：438套（占比18.6%）\n- 60000-80000元/㎡：278套（占比11.8%）",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.8,
        "style_reasoning": "数据分布列表，包含市场分析属性",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_019",
      "original_content": "- 浦东新区：572套（占比24.3%）\n- 闵行区：245套（占比10.4%）\n- 宝山区：240套（占比10.2%）\n- 徐汇区：114套（占比4.8%）\n- 黄浦区：54套（占比2.3%）",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.8,
        "style_reasoning": "区域数据分布列表，包含市场分析属性",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_022",
      "original_content": "#### 月度成交趋势（2024.07-2025.06）\n| 月份   | 成交套数 | 成交均价(元/㎡) |\n|--------|----------|-----------------|\n| 2025/06| 2279     | 45739           |\n| 2025/05| 4780     | 47563           |\n| 2025/04| 4555     | 49617           |\n| 2025/03| 7099     | 49902           |",
      "content_type": "table",
      "recommended_widget": {
        "primary_type": "TABLE",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "市场趋势数据表格，包含时间序列和价格数据",
        "alternatives": [
          {
            "type": "CHART",
            "style": "LINE",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "时间序列数据适合折线图展示"
          }
        ]
      },
      "processing_notes": "结构优先原则：表格结构优先推荐TABLE控件"
    },
    {
      "segment_id": "seg_024",
      "original_content": "#### 土地供应数据\n- 供应峰值：2024年9月（506929㎡）\n- 2025年6月：485775㎡\n- 楼板价波动区间：29283-84204元/㎡",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.8,
        "style_reasoning": "土地市场数据列表，包含分析属性",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_027",
      "original_content": "#### 面积段成交分布\n- 70-90㎡：588套（占比25.0%）\n- 50-70㎡：576套（占比24.5%）\n- 90-110㎡：334套（占比14.2%）",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.8,
        "style_reasoning": "户型面积数据列表，包含分析属性",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_029",
      "original_content": "#### 新房户型偏好\n- 三房户型：3068套（占比67%）\n- 四房户型：824套（占比18%）",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.9,
        "style_confidence": 0.8,
        "style_reasoning": "新房户型数据列表，包含分析属性",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    },
    {
      "segment_id": "seg_038",
      "original_content": "- **继承后转让税负**：再次出售需按差额20%缴个税，务必保留原购房凭证\n- **特殊房产限制**：拆迁安置房、共有产权房继承有特殊规定\n- **涉外继承**：涉及境外因素需专业法律支持\n- **未成年人继承**：处置流程复杂需提前规划\n- **债务风险**：继承房产同时继承相关房贷等债务",
      "content_type": "list",
      "recommended_widget": {
        "primary_type": "LIST",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "风险要点列表，包含专业法律和税务内容",
        "alternatives": [
          {
            "type": "LIST",
            "style": "BULLET",
            "type_confidence": 0.9,
            "style_confidence": 0.8,
            "reasoning": "标准符号列表格式"
          }
        ]
      },
      "processing_notes": "结构优先原则：符号列表结构优先推荐LIST控件"
    }
  ],
  "processing_metadata": {
    "step": 2,
    "widgets_generated": {
      "TITLE": 16,
      "TEXT": 12
    },
    "serial_mapping": {
      "seg_001": "0",
      "seg_002": "1",
      "seg_003": "1.1",
      "seg_004": "2",
      "seg_005": "2.1",
      "seg_007": "2.2",
      "seg_009": "3",
      "seg_010": "3.1",
      "seg_011": "3.1.1",
      "seg_013": "3.1.2",
      "seg_014": "3.1.3",
      "seg_016": "3.1.4",
      "seg_017": "3.2",
      "seg_018": "3.2.1",
      "seg_020": "3.2.2",
      "seg_021": "3.3",
      "seg_023": "3.3.1",
      "seg_025": "3.3.3",
      "seg_026": "3.4",
      "seg_028": "3.4.2",
      "seg_030": "3.4.4",
      "seg_031": "4",
      "seg_032": "4.1",
      "seg_033": "4.2",
      "seg_034": "4.3",
      "seg_035": "4.4",
      "seg_036": "4.5",
      "seg_037": "5",
      "seg_039": "5.1"
    },
    "type_adjustments": [],
    "remaining_types": [
      "LIST",
      "TABLE",
      "CHART",
      "CARD"
    ],
    "processing_notes": "基础控件生成完成，全局编号已预分配"
  }
}
```