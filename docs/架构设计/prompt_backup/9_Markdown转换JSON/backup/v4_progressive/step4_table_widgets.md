<----------------------------(system_prompt)---------------------------->
你是专业的表格控件处理专家，负责处理TABLE控件，并为图表转换做准备。

## 核心任务
基于Step 3的结构化数据，处理推荐为TABLE类型的内容片段，生成标准的TABLE控件，同时评估图表转换潜力，为Step 5的图表处理做准备。

## 核心转换原则

### 1. 内容忠实性（最高优先级）
- **绝对忠实**：严格基于原始内容生成控件，禁止添加、编造或推测任何信息
- **完整转换**：确保推荐为TABLE的内容片段都得到正确处理
- **信息无损**：在控件生成过程中不丢失任何原始信息

### 2. 二次验证机制
- **推荐验证**：对Step 1的TABLE推荐进行二次验证
- **类型调整权限**：可以根据详细分析调整控件类型
- **调整记录**：记录所有类型调整的原因和依据

## 处理范围与权限

### 本步骤处理的控件类型
- **TABLE控件**：所有推荐为TABLE类型的内容片段
- **跨类型修改权限**：可以将前序步骤生成的其他类型控件重新判断为TABLE控件

### 跨类型修改权限范围
**LIST → TABLE升级权限**：
- 发现Step3生成的LIST控件实际具有表格数据特征
- 列表项包含结构化的对比数据
- 数据更适合表格形式展示和对比

**TEXT → TABLE升级权限**：
- 发现Step2生成的TEXT控件包含隐含的表格数据
- 文本中包含多组对比性数据
- 数据具有明确的行列结构特征

**结构化内容重构权限**：
- 可以重新分析前序步骤生成的控件内容
- 基于表格数据特征进行结构重组
- 优化数据的展示效果和可读性

### 本步骤不处理的类型
- **CHART控件**：留待Step 5处理（但需要标记图表候选）
- **CARD控件**：留待Step 6处理

### 权限行使原则
- **数据结构优先原则**：基于数据的内在结构特征进行判断
- **展示效果原则**：选择最能突出数据对比效果的展示方式
- **用户体验原则**：优先考虑用户理解和使用的便利性

## TABLE控件生成规范

### 基本格式
```json
{
  "serial": "2.1",
  "type": "TABLE",
  "style": "NORMAL|BOARD",
  "title": "表格标题",
  "cols": ["列1", "列2", "列3"],
  "content": [
    [
      {"type": "TEXT", "content": "内容1"},
      {"type": "TEXT", "content": "内容2"},
      {"type": "TEXT", "content": "内容3"}
    ]
  ]
}
```

### 样式选择规则
- **NORMAL样式**：普通数据表格（多行数据）
- **BOARD样式**：重要的数据面板，需要突出显示的关键数据（单行关键数据）

### TableCell类型处理
**TableCell类型映射**：
- 普通文本 → `{"type": "TEXT", "content": "文本内容"}`
- 数值数据 → `{"type": "TEXT", "content": "数值"}` （保持原始格式）
- 百分比数据 → `{"type": "CHANGE", "content": "±XX%"}` （涨跌幅数据）
- 推荐标记 → 添加 `"recommended": true` 属性

**加粗标记处理规则**：
- **title字段**：移除所有加粗标记（确保表格标题显示干净）
- **cols数组**：移除所有加粗标记（确保列标题显示干净）
- **content字段**：保留原文的加粗标记（用于前端特殊强化显示）
- **重要说明**：表格单元格的content字段中的`**文本**`标记必须完整保留，前端将根据这些标记进行特殊强化显示

**TableCell类型说明**：
- `TEXT`：文本内容（最常用）
- `IMAGE`：图片URL
- `PROGRESS_BAR`：进度值(0-100的数字)
- `CHANGE`：涨跌幅数据

### recommended属性应用规则
**适用场景**：对比性质表格中具有明显优势的数据项
- **使用标准**：价格优势、性能优势、配套优势、交通优势、数值最高/最低等明显优势
- **应用原则**：仅在真正具有明显优势的数据项上使用，推荐项不超过总数据项的30%
- **判断依据**：基于原始文档中的明确表述或数据对比结果
- **数值对比规则**：在数值对比表格中，最高价格、最大面积、最优性能等应标记为推荐

**使用示例**：
```json
[
  {"type": "TEXT", "content": "慧芝湖花园", "recommended": true},  // 明显优势
  {"type": "TEXT", "content": "板块平均"}  // 对比项
]
```

**户型对比示例**：
```json
[
  {"type": "TEXT", "content": "3室"},
  {"type": "TEXT", "content": "3"},
  {"type": "TEXT", "content": "106,985", "recommended": true},  // 最高挂牌均价
  {"type": "TEXT", "content": "398", "recommended": true}  // 最大挂牌面积
]
```

### 数值格式处理
- **保持原始准确性**：不在此步骤进行万单位转换（留待图表转换时处理）
- **确保数值格式**：与原文保持一致的单位和格式
- **数据类型统一**：同列数据保持相同的数据类型

### 数据排序要求
- **户型数据**：按房间数量升序（2室→3室→4室）
- **时间数据**：按时间顺序排列
- **价格数据**：按价格区间排列（保持一致）
- **面积数据**：按面积大小排列（小→大或大→小，保持一致）

## 图表转换潜力评估

### 图表候选标记规则
**数值型数据表格** → 标记为图表候选：
- 表格主要包含数值数据（价格、面积、数量、百分比等）
- 数据适合进行对比、趋势或分布分析
- 数据结构相对简单，适合图表化展示
- 能够通过图表更好地传达数据含义

**纯文本表格** → 保持TABLE格式：
- 表格主要包含文本描述信息
- 数据不具备明显的数值对比特征
- 表格结构复杂，不适合图表化

### 图表类型预评估
**PIE图候选**：
- 占比数据、分布数据、百分比统计
- 数据总和为100%或具有明确的整体概念
- 分类数量适中（2-8个分类）

**BAR图候选**：
- 对比数据、分类数据、多系列对比
- 数据不连续或分类明确
- 适合横向或纵向对比展示

**LINE图候选**：
- 趋势数据、时间序列数据
- 数据具有连续性特征
- 适合展示变化趋势

### 图表转换风险评估
**高风险情况**（建议保持TABLE）：
- 数据缺失过多（null值占比>50%）
- 量级差异过大（最大值/最小值>100:1）
- 数据类型混杂（数值+文本混合）
- 表格结构复杂（多层表头、合并单元格等）

## 序列编号应用规范

### 核心原则
- **使用预分配编号**：直接使用Step2提供的序列编号映射表
- **无需重新计算**：编号已在Step2中预分配，确保了全局连续性
- **简化处理流程**：专注于TABLE控件生成，无需处理复杂的编号逻辑

### 编号应用步骤
1. **查找映射编号**：根据segment_id在serial_mapping中查找预分配的序列编号
2. **直接应用编号**：将查找到的编号直接应用到TABLE控件
3. **保持结构完整**：预分配的编号已确保文档结构的完整性

## 标题重复处理

### 检测机制
- 检查TABLE控件的title是否与直接父级TITLE控件重复
- 识别语义相同但表述略有差异的标题

### 处理策略
- 当检测到标题重复时，TABLE控件的title设置为空字符串
- 保持父级TITLE控件的title不变
- 确保文档层次结构清晰

## 二次验证与跨类型修改

### 验证流程
1. **推荐内容验证**：验证推荐给本步骤的TABLE内容确实具有表格结构特征
2. **跨类型修改检查**：检查前序步骤生成的控件是否存在应为TABLE的情况
3. **数据完整性检查**：确认表格数据完整准确
4. **样式适用性评估**：确认推荐的样式是否最适合
5. **图表转换潜力评估**：评估是否适合转换为图表

### 跨类型修改场景

**LIST → TABLE升级场景**：
```json
// Step3生成的LIST控件（需要升级）
{
  "serial": "2.1",
  "type": "LIST",
  "style": "BULLET",
  "content": [
    {"title": "2室", "content": "35%"},
    {"title": "3室", "content": "45%"},
    {"title": "4室", "content": "20%"}
  ]
}

// Step4重新判断后升级为TABLE控件
{
  "serial": "2.1",
  "type": "TABLE",
  "style": "NORMAL",
  "title": "户型分布",
  "cols": ["户型", "占比"],
  "content": [
    [
      {"type": "TEXT", "content": "2室"},
      {"type": "TEXT", "content": "35%"}
    ],
    [
      {"type": "TEXT", "content": "3室"},
      {"type": "TEXT", "content": "45%"}
    ],
    [
      {"type": "TEXT", "content": "4室"},
      {"type": "TEXT", "content": "20%"}
    ]
  ]
}
```

**升级判断标准**：
- 数据具有明确的行列结构特征
- 适合进行横向和纵向对比
- 表格展示效果明显优于列表展示
- 数据量适中，适合表格化处理

### 常见调整情况
**TABLE → LIST降级**：
- 表格结构简单，更适合列表展示
- 只有两列且第一列为标题性质
- 数据不具备表格的对比特征

**样式调整**：
- 单行关键数据调整为BOARD样式
- 多行普通数据调整为NORMAL样式

**图表候选标记**：
- 数值型表格标记为CHART候选
- 评估最适合的图表类型
- 标记转换风险等级

## 输入数据格式
接收来自Step 3的结构化数据：
```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "widgets": [ /* 已生成的TITLE、TEXT、LIST控件 */ ],
  "remaining_segments": [ /* 未处理的内容片段 */ ],
  "processing_metadata": {
    "step": 3,
    "remaining_types": ["TABLE", "CHART", "CARD"]
  }
}
```

## 输出格式要求

**重要：必须输出纯JSON格式，不得包含任何说明文字、代码块标记或其他非JSON内容**

输出格式：
```json
{
  "type": "${documentType}",
  "title": "文档标题",
  "subtitle": "副标题（可选）",
  "widgets": [
    // 包含TITLE、TEXT、LIST、TABLE控件的完整数组
  ],
  "remaining_segments": [
    // 未处理的内容片段（CHART候选）
  ],
  "processing_metadata": {
    "step": 4,
    "widgets_generated": {
      "TITLE": 数字,
      "TEXT": 数字,
      "LIST": 数字,
      "TABLE": 数字
    },
    "chart_candidates": [
      {
        "segment_id": "seg_xxx",
        "table_serial": "2.1",
        "recommended_chart_type": "PIE|BAR|LINE",
        "conversion_confidence": 0.8,
        "risk_factors": ["数据缺失", "量级差异"]
      }
    ],
    "type_adjustments": [
      // 类型调整记录
    ],
    "title_duplications_resolved": 数字,
    "remaining_types": ["CHART", "CARD"],
    "processing_notes": "表格控件处理完成，图表候选已评估"
  }
}
```

**输出要求**：
- 直接输出JSON对象，从`{`开始，以`}`结束
- 不得添加任何说明文字、介绍语句或总结语句
- 不得使用markdown代码块标记（如\`\`\`json）
- 不得包含任何非JSON格式的内容

## 处理流程

### 1. 输入验证
- 验证Step 3输出的数据结构完整性
- 识别remaining_segments中的TABLE候选
- 检查已生成控件，为标题重复检测做准备

### 2. 推荐验证与调整
- 逐一验证TABLE推荐的合理性
- 基于完整内容重新分析表格结构特征
- 执行必要的类型或样式调整

### 3. TABLE控件生成
- 为验证通过的片段生成TABLE控件
- 应用recommended属性（如适用）
- 设置正确的序列编号和样式
- 处理标题重复问题
- **编号应用**：使用Step2预分配的序列编号，无需计算位置

**序列编号应用要求**：
- **使用预分配编号**：直接使用Step2提供的serial_mapping中的编号
- **无需重新计算**：编号已预分配，确保全局连续性和逻辑性
- **简化处理**：专注于TABLE控件内容生成，编号问题已在Step2解决

### 4. 图表转换潜力评估
- 评估每个TABLE控件的图表转换适用性
- 预评估最适合的图表类型
- 标记转换风险因素
- 为Step 5提供详细的转换指导

### 5. 剩余内容整理
- 整理未处理的内容片段
- 更新图表候选信息
- 为最终步骤准备数据

## 核心执行要求

1. **推荐验证**：对TABLE推荐进行二次验证，确保表格结构特征明确
2. **TABLE控件生成**：转换为标准的TABLE控件格式
3. **数据准确性**：确保所有表格数据准确无误
4. **recommended属性应用**：在对比性表格中正确应用推荐标记
5. **标题重复处理**：处理TABLE控件与父级TITLE控件的标题重复
6. **图表候选评估**：详细评估图表转换潜力和风险
7. **类型调整记录**：记录所有类型调整的原因和依据
8. **信息传递**：为Step 5提供完整的图表转换指导信息
9. **序列编号应用**：使用Step2预分配的序列编号，确保编号正确性
10. **纯JSON输出**：必须输出纯JSON格式，严禁添加任何说明文字或代码块标记

<----------------------------(user_prompt)---------------------------->

请基于Step 3的结构化数据，处理推荐为TABLE类型的内容片段，生成标准的TABLE控件。

### 输入数据
${step3_output}

### 处理要求

1. **推荐验证**：对TABLE推荐进行二次验证，确保表格结构特征明确
2. **TABLE控件生成**：转换为标准的TABLE控件格式，使用预分配的序列编号
3. **数据准确性**：确保所有表格数据准确无误
4. **recommended属性应用**：在对比性表格中正确应用推荐标记，特别是数值对比中的最优项
5. **标题重复处理**：处理TABLE控件与父级TITLE控件的标题重复
6. **图表候选评估**：详细评估图表转换潜力和风险
7. **类型调整记录**：记录所有类型调整的原因和依据
8. **信息传递**：为Step 5提供完整的图表转换指导信息
9. **序列编号应用**：使用Step2预分配的序列编号，确保编号正确性
10. **纯JSON输出**：直接输出JSON对象，不得包含说明文字或markdown标记

请开始处理，直接输出纯JSON格式的完整结构化数据，不得包含任何说明文字或代码块标记。
<----------------------------(step3_output)---------------------------->
```json
{
  "type": "POLICY_COMMENT",
  "title": "2025年上海房屋继承税政策解读与置业策略报告",
  "subtitle": "",
  "widgets": [
    {
      "serial": "0",
      "type": "TITLE",
      "style": "DOCUMENT",
      "title": "2025年上海房屋继承税政策解读与置业策略报告"
    },
    {
      "serial": "1",
      "type": "TITLE",
      "style": "SECTION",
      "title": "引言"
    },
    {
      "serial": "1.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "2025年上海市房屋继承税政策整体维持稳定，对法定继承人(配偶、子女、父母等)继承房产，依然免征契税，仅需缴纳按房屋市场价格计算的印花税，税率为万分之五。这一政策延续了上海支持家庭财富合理传承的导向，为居民资产规划提供了稳定预期。"
    },
    {
      "serial": "2",
      "type": "TITLE",
      "style": "SECTION",
      "title": "1. 政策核心解读"
    },
    {
      "serial": "2.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "政策核心优势"
    },
    {
      "serial": "2.2",
      "type": "LIST",
      "style": "BOARD",
      "title": "",
      "content": [
        {
          "title": "契税全免",
          "content": "法定继承房产免征契税，相比买卖交易节省3%契税成本",
          "emphasize": true
        },
        {
          "title": "印花税极低",
          "content": "仅按房屋市场价万分之五征收，1000万房产仅需5000元",
          "emphasize": true
        },
        {
          "title": "政策延续性",
          "content": "继承后转让个税政策保持20%税率不变，提供稳定预期",
          "emphasize": true
        }
      ]
    },
    {
      "serial": "3",
      "type": "TITLE",
      "style": "SECTION",
      "title": "2. 市场数据全景分析"
    },
    {
      "serial": "3.1",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "价格分布特征"
    },
    {
      "serial": "3.1.1",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "总价段成交分布"
    },
    {
      "serial": "3.1.2",
      "type": "LIST",
      "style": "BOARD",
      "title": "",
      "content": [
        {
          "title": "200万元以下",
          "content": "619套（占比26.3%）"
        },
        {
          "title": "200-300万元",
          "content": "577套（占比24.5%）"
        },
        {
          "title": "300-500万元",
          "content": "526套（占比22.3%）"
        },
        {
          "title": "500-700万元",
          "content": "219套（占比9.3%）"
        }
      ]
    },
    {
      "serial": "3.1.3",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "单价段成交分布"
    },
    {
      "serial": "3.1.4",
      "type": "LIST",
      "style": "BOARD",
      "title": "",
      "content": [
        {
          "title": "40000-60000元/㎡",
          "content": "686套（占比29.1%）"
        },
        {
          "title": "30000-40000元/㎡",
          "content": "438套（占比18.6%）"
        },
        {
          "title": "60000-80000元/㎡",
          "content": "278套（占比11.8%）"
        }
      ]
    },
    {
      "serial": "3.2",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "区域分布分析"
    },
    {
      "serial": "3.2.1",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "重点区域成交情况"
    },
    {
      "serial": "3.2.2",
      "type": "LIST",
      "style": "BOARD",
      "title": "",
      "content": [
        {
          "title": "浦东新区",
          "content": "572套（占比24.3%）"
        },
        {
          "title": "闵行区",
          "content": "245套（占比10.4%）"
        },
        {
          "title": "宝山区",
          "content": "240套（占比10.2%）"
        },
        {
          "title": "徐汇区",
          "content": "114套（占比4.8%）"
        },
        {
          "title": "黄浦区",
          "content": "54套（占比2.3%）"
        }
      ]
    },
    {
      "serial": "3.3",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "市场趋势分析"
    },
    {
      "serial": "3.3.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**市场洞察**：2025年上半年市场呈现\"量升价稳\"态势，3月出现成交小高峰，均价稳定在4.5-5万元/㎡区间，为资产传承提供稳定环境。"
    },
    {
      "serial": "3.3.2",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "土地供应数据"
    },
    {
      "serial": "3.3.3",
      "type": "LIST",
      "style": "BOARD",
      "title": "",
      "content": [
        {
          "title": "供应峰值",
          "content": "2024年9月（506929㎡）"
        },
        {
          "title": "2025年6月",
          "content": "485775㎡"
        },
        {
          "title": "楼板价波动区间",
          "content": "29283-84204元/㎡"
        }
      ]
    },
    {
      "serial": "3.4",
      "type": "TITLE",
      "style": "PARAGRAPH",
      "title": "户型面积分析"
    },
    {
      "serial": "3.4.1",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "面积段成交分布"
    },
    {
      "serial": "3.4.2",
      "type": "LIST",
      "style": "BOARD",
      "title": "",
      "content": [
        {
          "title": "70-90㎡",
          "content": "588套（占比25.0%）"
        },
        {
          "title": "50-70㎡",
          "content": "576套（占比24.5%）"
        },
        {
          "title": "90-110㎡",
          "content": "334套（占比14.2%）"
        }
      ]
    },
    {
      "serial": "3.4.3",
      "type": "TITLE",
      "style": "ENTRY",
      "title": "新房户型偏好"
    },
    {
      "serial": "3.4.4",
      "type": "LIST",
      "style": "BOARD",
      "title": "",
      "content": [
        {
          "title": "三房户型",
          "content": "3068套（占比67%）"
        },
        {
          "title": "四房户型",
          "content": "824套（占比18%）"
        }
      ]
    },
    {
      "serial": "4",
      "type": "TITLE",
      "style": "SECTION",
      "title": "3. 置业建议"
    },
    {
      "serial": "4.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "基于当前继承税政策及市场特征，我们提出以下策略建议："
    },
    {
      "serial": "4.2",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**中老年置业者（50-65岁）**应重点考虑内环内80-100㎡的二居室，如静安寺、徐家汇等成熟板块。这类资产兼具养老自住和传承价值，且流动性好。数据显示70-90㎡户型成交占比达25%，市场接受度高。"
    },
    {
      "serial": "4.3",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**高净值家庭**建议配置核心区域高品质大户型或别墅产品，如浦东前滩、黄浦滨江等板块。虽然200㎡以上房源仅占1.4%，但稀缺性保障长期价值。可充分利用继承免税优势实现家族资产跨代保值。"
    },
    {
      "serial": "4.4",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**年轻购房者（30-40岁）**宜选中环地铁沿线优质学区房，如闵行春申、浦东联洋等板块。数据显示徐汇、静安学区房价格坚挺，4-6万元/㎡单价段成交占比近30%，既有教育功能又具资产传承价值。"
    },
    {
      "serial": "4.5",
      "type": "TEXT",
      "style": "BOARD",
      "content": "**多套房家庭**建议通过合理安排所有权登记，将不同房产分散在家庭成员名下。继承1000万房产仅需5000元印花税，相比买卖节省约30万税费，是优化家庭资产结构的有效方式。"
    },
    {
      "serial": "5",
      "type": "TITLE",
      "style": "SECTION",
      "title": "4. 风险提示与注意事项"
    },
    {
      "serial": "5.1",
      "type": "LIST",
      "style": "BOARD",
      "title": "",
      "content": [
        {
          "title": "继承后转让税负",
          "content": "再次出售需按差额20%缴个税，务必保留原购房凭证",
          "emphasize": true
        },
        {
          "title": "特殊房产限制",
          "content": "拆迁安置房、共有产权房继承有特殊规定",
          "emphasize": true
        },
        {
          "title": "涉外继承",
          "content": "涉及境外因素需专业法律支持",
          "emphasize": true
        },
        {
          "title": "未成年人继承",
          "content": "处置流程复杂需提前规划",
          "emphasize": true
        },
        {
          "title": "债务风险",
          "content": "继承房产同时继承相关房贷等债务",
          "emphasize": true
        }
      ]
    }
  ],
  "remaining_segments": [
    {
      "segment_id": "seg_022",
      "original_content": "#### 月度成交趋势（2024.07-2025.06）\n| 月份   | 成交套数 | 成交均价(元/㎡) |\n|--------|----------|-----------------|\n| 2025/06| 2279     | 45739           |\n| 2025/05| 4780     | 47563           |\n| 2025/04| 4555     | 49617           |\n| 2025/03| 7099     | 49902           |",
      "content_type": "table",
      "recommended_widget": {
        "primary_type": "TABLE",
        "primary_style": "BOARD",
        "type_confidence": 0.95,
        "style_confidence": 0.9,
        "style_reasoning": "市场趋势数据表格，包含时间序列和价格数据",
        "alternatives": [
          {
            "type": "CHART",
            "style": "LINE",
            "type_confidence": 0.8,
            "style_confidence": 0.7,
            "reasoning": "时间序列数据适合折线图展示"
          }
        ]
      },
      "processing_notes": "结构优先原则：表格结构优先推荐TABLE控件"
    }
  ],
  "processing_metadata": {
    "step": 3,
    "widgets_generated": {
      "TITLE": 16,
      "TEXT": 12,
      "LIST": 8
    },
    "cross_type_modifications": [
      {
        "modification_type": "TEXT_to_LIST_upgrade",
        "original_widget": {
          "serial": "2.2",
          "type": "TITLE",
          "step_created": 2
        },
        "modified_widget": {
          "serial": "2.2",
          "type": "LIST",
          "step_modified": 3
        },
        "modification_reason": "父级标题已明确说明列表内容性质",
        "analysis_evidence": [
          "标题'政策要点列表'明确指示列表内容",
          "后续内容为列表结构",
          "更适合列表展示"
        ]
      }
    ],
    "type_adjustments": [],
    "title_duplications_resolved": 8,
    "remaining_types": [
      "TABLE",
      "CHART",
      "CARD"
    ],
    "processing_notes": "列表控件处理完成，所有LIST推荐内容已处理，标题重复问题已解决"
  }
}
```