请将以下房产生活分析报告转换为DocumentData结构化对象，**重点提炼配套设施和生活便利性信息**，帮助购房者快速了解区域生活配套和置业价值：

**原始分析报告内容：**
```
${request.prevGenerateResponse}
```

**转换目标：**
将复杂的房产生活分析报告转换为购房者友好的结构化对象，**充分挖掘原报告的配套设施信息**，在保持购房者友好的同时，**确保配套信息的完整性和实用性**，让购房者获得全面深入的生活配套分析和置业建议。

## 具体要求

### 核心提炼要求

1. **配套设施提炼**：
   - 从报告中提炼最关键的配套升级信息（如：交通网络完善、教育资源集聚、医疗配套升级）
   - 突出配套设施的实际影响和生活便利性（如：通勤时间缩短、学区价值提升、就医便利）
   - 强调配套完善对居住体验的重要意义

2. **数据价值挖掘**：
   - 从众多图表中选择最能说明区域发展和配套价值的关键数据
   - 重点关注：土地供应数据、新房供求数据、二手房成交数据、区域分布数据
   - 突出数据背后的配套发展趋势和置业机会

3. **置业建议聚焦**：
   - 将复杂的区域分析转化为具体可操作的置业策略
   - 针对不同购房群体（刚需、改善、投资）提供精准建议
   - 突出配套优势和投资价值

### 图表类型使用指导

**重要：必须使用多样化的图表类型，充分展现房产市场数据！**

1. **饼图(PIE)**：用于展示分类占比数据
   - 区域成交分布、库存分布、面积段分布、价格段分布等
   - 格式：不需要cols字段，content为对象数组

2. **柱状图(BAR)**：用于展示时间序列数据、多系列对比
   - 月度土地数据、月度供求数据、月度成交数据等
   - 格式：必须包含cols字段（横轴类别），content为系列数据数组

3. **折线图(LINE)**：用于展示趋势变化
   - 价格走势、成交趋势、库存变化等
   - 格式：与柱状图相同，只是style为"LINE"

**图表格式示例：**

```json
// 饼图示例
{
  "serial": "2.1",
  "type": "CHART",
  "style": "PIE",
  "title": "杭州新房2025年06月区域成交分布",
  "content": [
    {"title": "萧山区", "content": 12.27},
    {"title": "余杭区", "content": 11.5}
  ]
}

// 柱状图示例
{
  "serial": "2.5",
  "type": "CHART",
  "style": "BAR",
  "title": "杭州2024年07月-2025年07月月度土地数据",
  "cols": ["2024/07", "2024/08", "2024/09"],
  "content": [
    {
      "title": "供应总建",
      "content": [333093, 428714, 395030]
    },
    {
      "title": "成交总建", 
      "content": [185874, 333093, 428714]
    }
  ]
}
```

### 特殊控件使用指导

1. **HOUSING_CARD控件**：用于房源推荐
```json
{
  "serial": "3.1",
  "type": "HOUSING_CARD",
  "name": "小区名称",
  "layout": "户型",
  "area": "面积",
  "floor": "楼层",
  "location": "位置",
  "price": "总价",
  "unit_price": "单价",
  "tags": ["标签1", "标签2"]
}
```

2. **BLOCK样式**：用于重要提示和本地洞察
```json
{
  "serial": "1.2",
  "type": "TEXT",
  "style": "BLOCK",
  "title": "本地洞察",
  "content": "重要的配套信息或市场洞察"
}
```

### 推荐结构组织

**注意：以下结构仅为参考，实际生成应根据原报告内容扩展，不要被限制**

```json
[
  {"serial": "1", "type": "TEXT", "style": "SECTION", "title": "配套升级核心亮点"},
  {"serial": "1.1", "type": "TEXT", "style": "EMPHASIS", "content": "核心配套优势"},
  {"serial": "1.2", "type": "TEXT", "style": "BLOCK", "title": "本地洞察", "content": "重要配套信息"},
  
  {"serial": "2", "type": "TEXT", "style": "SECTION", "title": "交通配套分析"},
  {"serial": "2.1", "type": "TEXT", "style": "PLAIN", "content": "交通配套详情"},
  {"serial": "2.2", "type": "CHART", "style": "BAR", "title": "月度土地供应数据"},
  
  {"serial": "3", "type": "TEXT", "style": "SECTION", "title": "教育医疗配套"},
  {"serial": "3.1", "type": "LIST", "style": "ITEM", "title": "教育资源分布"},
  {"serial": "3.2", "type": "CHART", "style": "PIE", "title": "区域库存分布"},
  
  {"serial": "4", "type": "TEXT", "style": "SECTION", "title": "商业绿化配套"},
  {"serial": "4.1", "type": "TEXT", "style": "PLAIN", "content": "商业配套分析"},
  {"serial": "4.2", "type": "CHART", "style": "BAR", "title": "月度成交数据"},
  
  {"serial": "5", "type": "TEXT", "style": "SECTION", "title": "区域投资价值分析"},
  {"serial": "5.1", "type": "TABLE", "title": "区域房价对比"},
  {"serial": "5.2", "type": "CHART", "style": "LINE", "title": "价格走势分析"},
  
  {"serial": "6", "type": "TEXT", "style": "SECTION", "title": "置业建议"},
  {"serial": "6.1", "type": "LIST", "style": "SERIAL", "title": "核心建议（按重要性排序）"},
  {"serial": "6.2", "type": "TEXT", "style": "BLOCK", "title": "好房推荐", "content": "推荐房源"},
  {"serial": "6.3", "type": "HOUSING_CARD", "name": "推荐房源"}
]
```

## 重要注意事项

- **必须设置所有控件的type和serial字段**
- **必须包含subtitle字段** - 生成简洁的副标题补充说明
- **充分挖掘原报告的配套设施信息** - 这是最重要的要求
- **图表类型必须多样化** - 必须同时包含饼图(PIE)、柱状图(BAR)、折线图(LINE)
- **原报告中的月度数据必须转换为柱状图或折线图**
- **配套信息要全面** - 涵盖交通、教育、医疗、商业、绿化等各方面
- **不要被推荐结构限制** - 可以创建更多章节和控件，充分展现原报告内容
- **目标控件数量：15-25个** - 确保内容的丰富性和完整性
- **目标图表数量：6-10个** - 尽可能转换原报告中的所有有价值图表
- **可以扩展到第7、8章节** - 如果原报告内容支持，不要局限于6个章节

## 输出要求

请将转换后的结果以标准JSON格式输出，**充分挖掘原报告的配套设施和数据价值**，为购房者提供全面深入的生活配套分析和置业建议。

**特别强调：**
1. **每个控件都必须包含正确的serial字段编号**
2. **配套信息完整性比简洁性更重要** - 确保充分利用原报告的所有配套信息
3. **图表类型必须多样化** - 必须同时包含饼图(PIE)、柱状图(BAR)、折线图(LINE)
4. **原报告中的月度数据必须转换为柱状图或折线图**
5. **生成的JSON应该充分体现生活配套价值，而不是简单的数据罗列**
