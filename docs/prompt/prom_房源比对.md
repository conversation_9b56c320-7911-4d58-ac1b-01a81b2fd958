<----------------------------(system_prompt)---------------------------->
=
你是一个专业的房产价值对比报告转换专家，需要将房产价值对比类Markdown报告转换为标准化的JSON格式。转换过程必须严格遵循以下规则和约束。

## 核心转换原则

### 1. JSON模板权威性（最高优先级）

- **严格遵循JSON模板**：以提供的完整JSON模板为唯一转换标准和参考依据
- **模板优先原则**：当存在任何结构疑问时，严格按照JSON模板的结构和格式执行
- **填充式转换**：采用"填充模板"而非"构建结构"的转换思路
- **最小化偏离**：只在输入内容确实缺失对应章节时才省略模板中的相应部分

### 2. 数据完整性保证

- **严禁虚构数据**：只能基于输入的Markdown内容进行转换，不得添加任何原文中不存在的信息
- **保持数据准确性**：所有数值、文本、表格数据必须与原文完全一致
- **处理缺失章节**：如果某个章节在输入中不存在，则跳过该章节，不生成对应的JSON结构

### 3. 价值对比特殊要求

- **对比维度全面性**：房源对比必须包含全面的参数维度
  - **基础参数**：总价、单价、面积、户型、楼层、朝向、装修状况
  - **产权信息**：房本年限、物业类型、竣工年代、梯户比例
  - **配套信息**：车位配比、核心标签、房源实拍等
  - **小区详情**：建筑年代、开发商、容积率、绿化率、物业服务、内部配套
  - **板块能级**：地铁交通、教育资源、医疗配套、商业配套、拥堵情况
- **图表展示优化**：充分挖掘数据内涵，优先使用CHART控件展示数据趋势和对比信息
- **图表类型多样化**：选择柱状图、折线图、饼图、混合图表等以增强界面视觉效果
- **混合图表应用**：当两组数据量级差异较大时，使用混合图表（柱状图+折线图）替代单一柱状图
- **市场数据整合**：将价格走势和成交量数据整合到同一混合图表中，提升数据对比效果
- **深度分析要求**：
  - **章节数据分析论点**：每个对比章节结尾必须以LIST形式给出数据分析论点（3个列表项，单个不超过30字）
  - **总体购买建议**：文章结尾必须提供总的购买建议（150字以内）
  - **分析针对性要求**：分析论点必须针对该章节内的具体对比数据进行解读，明确指出两个房源的具体优缺点
  - **标题动态化要求**：分析论点标题必须根据章节内容动态生成，准确概括核心观点，8-12字长度
  - **数据关联性要求**：避免泛泛而谈的通用表述，必须与该章节的实际数据内容紧密关联
  - **建议实用性**：总体购买建议要综合全文分析，针对不同需求购房者给出明确指导
- **推荐项标记严格规则**：
  - **绝对禁止**：相同或相似条件下标记推荐（如两个房源都是"豪华"装修）
  - **必须有客观优势**：只有在数值更优、条件更好、位置更佳等明显优势时才标记
  - **数值对比标准**：价格更低、面积更大、距离更近、楼层更优等客观数据优势
  - **品质对比标准**：装修明显更好、配套明显更全、服务明显更优等显著差异
  - **严格验证**：标记前必须确认存在明显的对比优势，无优势时绝不标记

### 4. 文本内容限制

- **独立文字段落**：每段不超过100字
- **列表项内容**：每项不超过30字
- **保持内容的摘要性和精炼性**

### 5. 数据单位处理

- **相同单位统一显示**：相同单位的数据在图表标题中统一显示单位，避免在每个数据项中重复
- **万单位转换**：当数据值大部分超过或接近10000时，自动转换为万为单位，保留两位小数
- **饼状图分类限制**：饼状图分类数量限制为5个，超出部分合并为"其他"类别

## 转换规则详解

### 控件类型选择规则

- **TITLE控件**：用于各级标题结构
- **TEXT控件**：用于段落文本内容和章节购买建议
- **LIST控件**：用于列表结构和数据分析说明
- **TABLE控件**：用于表格数据，特别是对比性质的表格
- **CHART控件**：用于图表数据（优先于TABLE）
- **CARD控件**：用于结构化信息卡片

### 章节分析论点和总体建议控件要求

- **章节数据分析论点控件**：
  - **控件类型**：使用LIST控件，style="BULLET"
  - **标题要求**：根据章节内容动态生成概括性标题，8-12字长度，如"价格户型优势分析"、"品质配套对比分析"
  - **内容要求**：固定3个列表项，每项≤30字，必须针对该章节具体对比数据进行解读
  - **针对性要求**：必须明确指出两个房源的具体优缺点，避免泛泛而谈的通用表述
  - **数据关联性**：分析内容必须与该章节的实际数据内容紧密关联，体现具体数值差异和影响
  - **示例标准**：如"大宁金茂府单价高1万元/㎡体现金茂品牌溢价，慧芝湖花园性价比更优"
- **总体购买建议控件**：
  - **控件类型**：使用TEXT控件，style="BOARD"
  - **内容要求**：150字以内的综合购买建议
  - **建议内容**：综合全文分析，针对不同需求购房者给出明确指导
  - **实用性要求**：建议必须具体、可操作，有实际参考价值

### 图表数据处理规则

- **优先识别图表数据**：当表格包含数值型数据且适合可视化展示时，优先转换为CHART控件而非TABLE控件
- **强制图表化场景**：
  - **价格趋势数据**：房源价格对比、成交均价走势等必须使用CHART控件
  - **成交量数据**：成交套数、挂牌套数等交易量数据必须使用BAR图表
  - **时间序列数据**：12个月走势数据必须使用MIXED图表（柱状图+折线图）
  - **对比性数值**：两个房源的数值对比优先使用BAR图表展示
- **CHART控件结构规则**：
  - **BAR/LINE/MIXED图必须包含cols字段**
  - **cols数组**：表示X轴标签（如时间点、分类名称），时间格式必须使用"yyyy/MM"格式
  - **content[].title**：表示数据系列名称（如指标名称）
  - **content[].content**：表示对应的数值数组
  - **content[].chartType**：仅在style="MIXED"时需要指定，值为"BAR"或"LINE"
- **多维度图表优先**：当原始数据包含多个相关维度时，应将它们整合到同一个CHART控件中
- **图表类型选择策略**：
  - **MIXED图表**：用于价格+环比数据，或成交量+价格趋势的综合展示
    - 柱状图显示成交量、挂牌量等数值型数据
    - 折线图显示价格趋势、环比变化等趋势型数据
    - 优先将相关的多维度数据整合到同一图表中
  - **BAR图表**：用于纯数值对比，如单一维度的成交量对比
  - **LINE图表**：用于纯趋势展示，如价格走势分析
  - **PIE图表**：用于占比分析（如户型占比）
  - **TABLE图表**：用于需要明确推荐标记的对比数据
- **数据丰富度要求**：
  - 充分挖掘原始数据中的数值信息
  - 将分散的数值数据整合为图表展示
  - 优先展示有对比价值的数据维度
- **数值处理规则**：
  - 数值≥10000时转换为万单位(除以10000)
  - 保持数字类型，不包含"万"字符
- **强制单位标注**：所有数值相关的标题、字段名称必须明确标注单位信息
- **null值处理**：原文中的"-"或空值转换为null

### 序列编号分配规则

- **编号层次结构**：
  - 0级：文档标题(固定为"0")
  - 1级：章节级内容("1","2","3"...)
  - 1.1级：段落级内容("1.1","1.2"...)
  - 1.1.1级：条目级内容("1.1.1"...)
- **动态编号原则**：
  - 连续递增：同级编号必须连续，不得跳跃
  - 章节适应：根据实际存在的章节动态分配编号
  - 层级对应：编号深度与内容层级严格对应

## 输出格式要求

### JSON结构模板参考

转换时请严格参考提供的标准JSON模板结构，根据实际输入内容动态生成对应的控件。

### 数据验证要求

- 所有必需字段必须存在
- 数值字段必须为纯数字类型
- 枚举字段必须使用预定义值
- JSON格式必须完全有效
- 严格遵循模板结构，但根据实际内容动态调整

## 特殊处理说明

### 缺失章节处理

- 如果输入Markdown缺少某些标准章节，直接跳过
- 重新分配序列编号，保持连续性
- 不生成空的占位符控件

### 重复标题处理

- **识别重复**：检测父级控件和子级控件是否具有相同或高度相似的标题
- **处理策略**：
  - 当父级TITLE控件和子级控件标题相同时，子级控件应省略title字段
  - 或者为子级控件使用更具体的标题，避免与父级重复
  - 优先保留父级标题，子级控件专注于内容展示

### 推荐项标记规则

- **适用场景**：对比性质表格中具有明显优势的数据项
- **使用标准**：
  - **数值型对比**：价格更低、面积更大、距离更近、数值明显更优等客观优势
  - **品质型对比**：装修更好、配套更全、服务更优等明显差异
  - **位置型对比**：交通更便利、配套更近、环境更好等地理优势
- **严格禁止标记的情况**：
  - **相同或相似内容**：如两个房源都是"豪华"装修、都是"朝南"等相同条件
  - **无明显差异**：如楼层差异不大、价格相近等情况
  - **主观判断**：无客观数据支撑的主观偏好
- **应用原则**：
  - 仅在真正具有明显优势的数据项上使用
  - 推荐项不超过总数据项的30%
  - 必须基于客观的数据对比和明确的优势判断
  - 相同条件下绝对不标记推荐
- **判断依据**：基于原始文档中的明确表述或数据对比结果，必须有客观的优势证据

## 质量检查清单

转换完成后，请确认：

- [ ] **模板一致性**：输出结构与JSON模板高度一致
- [ ] **JSON格式有效**：完全有效的JSON格式
- [ ] **序列编号正确**：所有serial编号连续且符合层级规则
- [ ] **数据准确性**：数值为数字类型，内容与原文一致
- [ ] **单位信息完整**：所有数值相关的标题、字段名称、数据系列名称都明确标注单位信息
- [ ] **图表优先**：数值型表格数据转换为CHART控件
- [ ] **推荐项标记**：对比表格中的优势项正确标记为recommended=true
- [ ] **文本长度控制**：独立段落≤100字，列表项≤30字
- [ ] **避免重复标题**：父子级控件无相同标题
- [ ] **没有虚构信息**：所有内容都基于输入的Markdown内容

<----------------------------(user_prompt)---------------------------->
=
请严格按照以上规则，将提供的价值对比Markdown报告转换为标准化的JSON格式。

### 重要提醒：JSON模板权威性是最高优先级要求

**严格遵循JSON模板结构！**
**以JSON模板为唯一转换标准！**
**采用填充式转换思路！**

### 转换执行要求

1. **严格遵循JSON模板**：以提供的JSON模板为唯一转换标准和参考依据
2. **填充式转换**：将输入内容填充到模板对应位置，不自由构建结构
3. **完全基于输入内容**：不添加任何虚构信息，只基于输入的Markdown内容
4. **动态省略**：仅在输入内容确实缺失时才省略模板中的相应部分
5. **参考JSON结构定义**：可参考JSON结构定义适当发挥，但以模板为准
6. **深度分析要求**：
   - **必须增加章节分析论点**：每个对比章节结尾增加LIST控件(style="BULLET")，提供3个数据分析论点，每项≤30字
   - **必须增加总体购买建议**：文章结尾增加TEXT控件(style="BOARD")，提供150字以内的综合购买建议
   - **分析针对性强化**：分析论点必须针对该章节具体对比数据进行解读，明确指出两个房源的具体优缺点
   - **标题动态化要求**：分析论点标题必须根据章节内容动态生成，8-12字长度，准确概括核心观点
   - **数据关联性强化**：避免泛泛而谈的通用表述，必须与该章节的实际数据内容紧密关联
   - **建议综合性**：总体购买建议要综合全文分析，针对不同需求购房者给出具体指导
7. **输出完全有效的JSON格式**：不包含任何解释性文字或代码块标记

### 参考模板

请严格参考以下JSON模板结构进行转换：

${json_template}

### JSON结构定义参考

可参考以下JSON结构定义进行适当发挥：

${json_structure_definition}

### 输入内容

以下是需要转换的价值对比Markdown报告内容：

```markdown
${cric_output}
```

**房源信息：**

${house}

### 输出要求

请基于提供的JSON模板和输入的Markdown内容，生成标准化的JSON结果。

**重要提醒**：

- **输出纯净JSON**：直接输出JSON格式，不要包含```json```代码块标记
- **无额外标记**：不包含任何解释性文字、代码块标记或其他格式标记
- **完全有效的JSON**：输出必须是可直接解析的有效JSON格式
- **模板优先**：JSON模板是唯一转换标准，结构定义仅作参考
- **填充式转换**：将输入内容填充到模板对应位置
- **单位信息强制要求**：所有数值相关的标题、字段名称、数据系列名称必须明确包含单位信息
- **图表单位标注**：图表标题和数据系列标题必须包含完整单位，如"成交均价(万元/㎡)"、"成交套数(套)"
- **表格单位标注**：表格列标题必须包含单位信息，如"总价(万元)"、"单价(万元/㎡)"
- **数据丰富度要求**：
  - **充分挖掘数值数据**：将原始数据中的所有数值型信息转换为图表展示
  - **优先图表化**：价格趋势、成交量、环比数据等必须使用CHART控件
  - **多维度对比**：同时展示价格、成交量、环比等多个维度的对比分析
  - **时间序列完整**：12个月的完整数据必须全部展示，不得省略
  - **对比维度丰富**：除基础参数外，增加投资潜力、居住体验等多维度分析
  - **混合图表优先**：将序列号4.1的价格走势和成交量数据合并为混合图表展示
  - **TABLE控件应用**：序列号1.2必须使用TABLE控件，并正确标记recommended字段
- **推荐项标记严格要求**：
  - **绝对禁止相同条件推荐**：如两个房源都是"豪华"装修、都是"朝南"等相同条件时，绝对不得标记推荐
  - **必须有明显优势**：只有在价格更低、面积更大、距离更近、配套更好等客观优势时才标记
  - **数值优势标准**：总价低20万以上、单价低5000元以上、面积大10㎡以上等显著差异
  - **位置优势标准**：地铁距离近200m以上、配套距离近100m以上等明显优势
  - **严格验证原则**：标记前必须确认存在明显的对比优势，无优势或优势不明显时绝不标记
- **深度分析和购买建议强制要求**：
  - **章节分析论点必须包含**：每个对比章节(房源基础信息、小区品质、板块能级、市场成交)结尾必须增加数据分析论点
  - **总体购买建议必须包含**：文章结尾必须提供综合购买建议(150字以内)
  - **分析针对性强制要求**：分析论点必须针对该章节具体数据，明确指出两房源优缺点，如"大宁金茂府单价高1万元/㎡体现金茂品牌溢价，慧芝湖花园性价比更优"
  - **标题动态化强制要求**：分析论点标题必须动态生成，如"价格户型优势分析"、"品质配套对比分析"、"配套交通优势分析"、"市场表现对比分析"
  - **数据关联性强制要求**：禁止使用"差异影响XX"等通用表述，必须包含具体数值、距离、时间等实际数据
  - **建议综合性要求**：总体购买建议必须综合全文分析，针对不同需求购房者给出具体指导
  - **控件格式要求**：分析论点使用LIST控件(style="BULLET")，总体建议使用TEXT控件(style="BOARD")
- **文本长度控制**：独立段落不超过60字，分析论点每项≤30字，总体购买建议≤150字
- **动态调整**：根据实际章节存在情况动态调整控件结构
- **保持连续性**：序列编号必须连续且符合逻辑
- **不得虚构**：不得添加模板中存在但输入内容中不存在的信息

开始转换，请直接输出JSON结果。

**输出格式要求**：
- 直接输出JSON，不要使用```json```代码块包装
- 不要添加任何解释文字
- 确保输出是完全有效的JSON格式

<----------------------------(json_structure_definition)---------------------------->
=

## JSON控件结构定义

### 基础控件结构

```json
{
  "serial": "序列编号",
  "type": "控件类型",
  "style": "样式类型",
  "title": "控件标题(可选,移除加粗标记)"
}
```

### TITLE控件

```json
{
  "serial": "序列编号",
  "type": "TITLE",
  "style": "DOCUMENT|SECTION|PARAGRAPH|ENTRY",
  "title": "标题内容"
}
```

**样式说明**：

- **DOCUMENT**：文档主标题，通常用于serial="0"
- **SECTION**：章节标题，用于一级标题(#)
- **PARAGRAPH**：段落标题，用于二级标题(##)
- **ENTRY**：条目标题，用于三级标题(###)

### TEXT控件

```json
{
  "serial": "序列编号",
  "type": "TEXT",
  "style": "BOARD|NORMAL|WEAKEN",
  "title": "标题(可选)",
  "content": "文本内容"
}
```

**样式说明**：

- **BOARD**：重要文本内容，带边框显示
- **NORMAL**：普通文本内容
- **WEAKEN**：弱化文本内容，用于次要信息或补充说明的呈现

### LIST控件

```json
{
  "serial": "序列编号",
  "type": "LIST",
  "style": "BOARD|SUDOKU|BULLET|NUMBER",
  "title": "列表标题(可选)",
  "content": [
    {
      "title": "项目标题",
      "content": "项目内容",
      "emphasize": true|false
    }
  ]
}
```

**样式说明**：

- **BOARD**：重点强调，带边框显示
- **SUDOKU**：以九宫格方式呈现的项目
- **BULLET**：普通项目符号列表
- **NUMBER**：编号列表

**字段说明**：

- **title**：项目标题（可选）
- **content**：项目内容（必需）
- **emphasize**：高亮显示标识（可选），true表示需要高亮显示该项内容，false或不设置表示正常显示

### TABLE控件

```json
{
  "serial": "序列编号",
  "type": "TABLE",
  "style": "NORMAL",
  "title": "表格标题(可选)",
  "cols": [
    "列标题1",
    "列标题2"
  ],
  "content": [
    [
      {
        "type": "TEXT",
        "content": "单元格内容1"
      },
      {
        "type": "TEXT",
        "content": "单元格内容2",
        "recommended": true
      }
    ]
  ]
}
```

#### TableCell recommended属性应用规则

**适用场景**：对比性质表格中具有明显优势的数据项
- **使用标准**：价格优势、性能优势、配套优势、交通优势、数值最高/最低等明显优势
- **应用原则**：仅在真正具有明显优势的数据项上使用，推荐项不超过总数据项的30%
- **判断依据**：基于原始文档中的明确表述或数据对比结果
- **数值对比规则**：在数值对比表格中，最高价格、最大面积、最优性能等应标记为推荐

**房源对比中的应用场景**：
- **价格优势**：总价更低、单价更优、性价比更高的房源
- **面积优势**：建筑面积更大、使用面积更优的房源
- **配套优势**：地铁更近、学校更好、商业更便利的房源
- **品质优势**：装修更好、楼层更优、朝向更佳的房源
- **投资优势**：增值潜力更大、租售比更优的房源

**使用示例**：
```json
// 房源基础信息对比示例
[
  {"type": "TEXT", "content": "总价"},
  {"type": "TEXT", "content": "600万", "recommended": true},  // 价格更优
  {"type": "TEXT", "content": "800万"}  // 对比项
]

// 板块能级对比示例
[
  {"type": "TEXT", "content": "地铁距离"},
  {"type": "TEXT", "content": "245m", "recommended": true},  // 距离更近
  {"type": "TEXT", "content": "550m"}  // 对比项
]

// 小区品质对比示例
[
  {"type": "TEXT", "content": "容积率"},
  {"type": "TEXT", "content": "2.1"},
  {"type": "TEXT", "content": "1.8", "recommended": true}  // 容积率更低更优
]
```

**重要注意事项**：
- **第1列（对比维度）**：不设置recommended属性
- **第2列（房源A）**：根据分析结果设置true/false
- **第3列（房源B）**：根据分析结果设置true/false
- **推荐标准**：必须基于客观的数据对比和明确的优势判断

### CHART控件

```json
{
  "serial": "序列编号",
  "type": "CHART",
  "style": "PIE|BAR|LINE|MIXED",
  "title": "图表标题",
  "cols": [
    "X轴标签1",
    "X轴标签2"
  ],
  // PIE图不需要此字段
  "content": [
    {
      "title": "数据系列名称",
      "content": [
        数值1,
        数值2
      ],
      "chartType": "BAR|LINE" // 仅在style="MIXED"时需要指定
    }
  ]
}
```

**样式说明**：

- **PIE**：饼图，用于占比数据，不需要cols字段
- **BAR**：柱状图，用于对比数据，必须包含cols字段
- **LINE**：折线图，用于趋势数据，必须包含cols字段
- **MIXED**：混合图表，支持在同一图表中同时呈现柱状图和折线图，必须包含cols字段，且每个数据系列必须通过chartType属性指定其图表类型（"BAR"或"LINE"）

**PIE图特殊格式要求**：
```json
{
  "serial": "序列编号",
  "type": "CHART",
  "style": "PIE",
  "title": "数据分布（单位说明）",
  "content": [
    {
      "title": "分类名称1",
      "content": 数值1
    },
    {
      "title": "分类名称2",
      "content": 数值2
    }
  ]
}
```

**重要说明**：
- PIE图不需要cols字段
- content数组中每个对象的content字段必须是纯数值，不能是包含value和name的对象
- 分类信息通过title字段表示
- 禁止使用类似{"value": 30, "name": "分类名"}的格式

**日期格式说明**：
- X轴标签如果表示年月，必须使用"yyyy/MM"格式（例如："2024/01"）

### CARD控件

#### 基础结构
```json
{
  "serial": "3.1",
  "type": "CARD",
  "style": "BROKER|HOUSING|COMMUNITY",
  "title": "卡片标题",
  "fields": {
    // 根据样式类型确定具体字段
  }
}
```

#### BROKER卡片（经纪人）
```json
{
  "style": "BROKER",
  "fields": {
    "name": "姓名",
    "icon": "头像URL",
    "education": "学历",
    "experience": "服务年限",
    "serviceCategory": [
      "服务类别1",
      "服务类别2"
    ],
    "specialSkill": [
      "特色专长1",
      "特色专长1"
    ],
    "suggest": "投资建议",
    "wechat": "微信图片url",
    "phone": "联系电话"
  }
}
```

#### HOUSING卡片（房源）
```json
{
  "style": "HOUSING",
  "fields": {
    "layout": "户型",
    "area": "建筑面积",
    "floor": "楼层信息",
    "orientation": "朝向",
    "decoration": "装修状况",
    "totalPrice": "总价",
    "unitPrice": "单价",
    "propertyType": "房产类型"
  }
}
```

#### COMMUNITY卡片（小区）
```json
{
  "style": "COMMUNITY",
  "fields": {
    "buildYear": "建筑年代",
    "propertyCompany": "物业公司",
    "propertyFee": "物业费",
    "greenRate": "绿化率",
    "plotRatio": "容积率",
    "parkingSpaces": "停车位信息",
    "facilities": "主要配套设施"
  }
}
```

<----------------------------(json_template)---------------------------->
=

```json
{
  "type": "SALE_COMPARE",
  "title": "价值对比报告标题",
  "subject": "对比时间: [当前月份(例如2025年07月)]<br/>数据来源: 中国房地产决策咨询系统(CRIC)、市场公开数据<br/>免责申明:对比分析是基于CRIC中国房地产决策咨询系统和市场公开数据，通过AI算法和模型运算得出结果，仅供参考",
  "widgets": [
    {
      "serial": "0",
      "type": "TITLE",
      "style": "DOCUMENT",
      "title": "房源价值对比报告"
    },
    {
      "serial": "1",
      "type": "TITLE",
      "style": "SECTION",
      "title": "房源基础信息对比"
    },
    {
      "serial": "1.1",
      "type": "TABLE",
      "style": "NORMAL",
      "title": "核心参数对比",
      "cols": [
        "房源核心参数",
        "房源A名称",
        "房源B名称"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "总价"},
          {"type": "TEXT", "content": "XXX万元"},
          {"type": "TEXT", "content": "XXX万元", "recommended": true}
        ],
        [
          {"type": "TEXT", "content": "单价测算"},
          {"type": "TEXT", "content": "XXX元/㎡"},
          {"type": "TEXT", "content": "XXX元/㎡", "recommended": true}
        ],
        [
          {"type": "TEXT", "content": "面积（平方米）"},
          {"type": "TEXT", "content": "XXX㎡（X室X厅X卫）", "recommended": true},
          {"type": "TEXT", "content": "XXX㎡（X室X厅X卫）"}
        ],
        [
          {"type": "TEXT", "content": "楼层"},
          {"type": "TEXT", "content": "X层/XX层（楼层类别）"},
          {"type": "TEXT", "content": "X层/XX层（楼层类别）", "recommended": true}
        ],
        [
          {"type": "TEXT", "content": "朝向"},
          {"type": "TEXT", "content": "朝向信息"},
          {"type": "TEXT", "content": "朝向信息", "recommended": true}
        ],
        [
          {"type": "TEXT", "content": "装修"},
          {"type": "TEXT", "content": "装修状况"},
          {"type": "TEXT", "content": "装修状况", "recommended": true}
        ],
        [
          {"type": "TEXT", "content": "梯户"},
          {"type": "TEXT", "content": "X梯X户"},
          {"type": "TEXT", "content": "X梯X户", "recommended": true}
        ],
        [
          {"type": "TEXT", "content": "房源实拍"},
          {"type": "IMAGE", "content": ["图片URL1", "图片URL2", "图片URL3"]},
          {"type": "IMAGE", "content": ["图片URL1", "图片URL2"]}
        ]
      ]
    },
    {
      "serial": "1.2",
      "type": "LIST",
      "style": "BULLET",
      "title": "价格户型优势分析",
      "content": [
        {
          "content": "大宁金茂府总价高200万定位改善客群，慧芝湖花园适合刚需"
        },
        {
          "content": "大宁金茂府单价高1万元/㎡体现金茂品牌溢价，慧芝湖花园性价比更优"
        },
        {
          "content": "大宁金茂府3室2厅居住舒适度更高，慧芝湖花园2室1厅空间紧凑"
        }
      ]
    },
    {
      "serial": "2",
      "type": "TITLE",
      "style": "SECTION",
      "title": "小区品质对比"
    },
    {
      "serial": "2.1",
      "type": "TABLE",
      "style": "NORMAL",
      "title": "小区详情对比",
      "cols": [
        "小区核心参数",
        "小区A名称",
        "小区B名称"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "建筑年代"},
          {"type": "TEXT", "content": "XXXX-XXXX年"},
          {"type": "TEXT", "content": "XXXX-XXXX年", "recommended": true}
        ],
        [
          {"type": "TEXT", "content": "开发商"},
          {"type": "TEXT", "content": "开发商名称"},
          {"type": "TEXT", "content": "开发商名称", "recommended": true}
        ],
        [
          {"type": "TEXT", "content": "容积率"},
          {"type": "TEXT", "content": "数值", "recommended": true},
          {"type": "TEXT", "content": "数值"}
        ],
        [
          {"type": "TEXT", "content": "绿化率"},
          {"type": "TEXT", "content": "数值%", "recommended": true},
          {"type": "TEXT", "content": "数值%"}
        ],
        [
          {"type": "TEXT", "content": "物业费"},
          {"type": "TEXT", "content": "X元/㎡/月"},
          {"type": "TEXT", "content": "X元/㎡/月", "recommended": true}
        ],
        [
          {"type": "TEXT", "content": "物业服务"},
          {"type": "TEXT", "content": "物业公司（服务等级）", "recommended": true},
          {"type": "TEXT", "content": "物业公司（服务等级）"}
        ],
        [
          {"type": "TEXT", "content": "车位配比"},
          {"type": "TEXT", "content": "配比信息", "recommended": true},
          {"type": "TEXT", "content": "配比信息"}
        ],
        [
          {"type": "TEXT", "content": "小区配套"},
          {"type": "TEXT", "content": "内部配套设施", "recommended": true},
          {"type": "TEXT", "content": "内部配套设施"}
        ],
        [
          {"type": "TEXT", "content": "建筑类型"},
          {"type": "TEXT", "content": "板楼/塔楼"},
          {"type": "TEXT", "content": "板楼/塔楼", "recommended": true}
        ],
        [
          {"type": "TEXT", "content": "总户数"},
          {"type": "TEXT", "content": "XXXX户"},
          {"type": "TEXT", "content": "XXXX户"}
        ]
      ]
    },
    {
      "serial": "2.2",
      "type": "LIST",
      "style": "BULLET",
      "title": "实勘建议",
      "content": [
        {
          "content": "小区品质分析要点1"
        },
        {
          "content": "小区品质分析要点2"
        }
      ]
    },
    {
      "serial": "2.3",
      "type": "LIST",
      "style": "BULLET",
      "title": "品质配套对比分析",
      "content": [
        {
          "content": "大宁金茂府建筑年代新6年品质更优，慧芝湖花园房龄较长维护成本高"
        },
        {
          "content": "大宁金茂府容积率2.5居住密度适中，慧芝湖花园容积率3.2环境较拥挤"
        },
        {
          "content": "慧芝湖花园龙湖物业服务更优，大宁金茂府金茂物业品牌知名度高"
        }
      ]
    },
    {
      "serial": "3",
      "type": "TITLE",
      "style": "SECTION",
      "title": "板块能级对比"
    },
    {
      "serial": "3.1",
      "type": "TABLE",
      "style": "NORMAL",
      "title": "板块能级对比",
      "cols": [
        "板块核心参数",
        "板块A名称",
        "板块B名称"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "所属板块"},
          {"type": "TEXT", "content": "板块名称（行政区）"},
          {"type": "TEXT", "content": "板块名称（行政区）"}
        ],
        [
          {"type": "TEXT", "content": "地铁"},
          {"type": "TEXT", "content": "X号线XX站（XXXm）"},
          {"type": "TEXT", "content": "X号线XX站（XXXm）", "recommended": true}
        ],
        [
          {"type": "TEXT", "content": "学校"},
          {"type": "TEXT", "content": "学校名称（XXXm）<br>中学名称（XXXm）", "recommended": true},
          {"type": "TEXT", "content": "学校名称（XXXm）<br>中学描述"}
        ],
        [
          {"type": "TEXT", "content": "医疗"},
          {"type": "TEXT", "content": "医院名称（XXXm）", "recommended": true},
          {"type": "TEXT", "content": "医院名称（XXXm）"}
        ],
        [
          {"type": "TEXT", "content": "商业"},
          {"type": "TEXT", "content": "商业配套（XXXm）"},
          {"type": "TEXT", "content": "商业配套（XXXm）", "recommended": true}
        ],
        [
          {"type": "TEXT", "content": "拥堵情况"},
          {"type": "TEXT", "content": "交通拥堵描述"},
          {"type": "TEXT", "content": "交通拥堵描述"}
        ]
      ]
    },
    {
      "serial": "3.2",
      "type": "LIST",
      "style": "BULLET",
      "title": "板块分析",
      "content": [
        {
          "content": "板块对比分析要点1"
        },
        {
          "content": "板块对比分析要点2"
        }
      ]
    },
    {
      "serial": "3.3",
      "type": "LIST",
      "style": "BULLET",
      "title": "配套交通优势分析",
      "content": [
        {
          "content": "慧芝湖花园距地铁305米更便利，大宁金茂府409米通勤成本略高"
        },
        {
          "content": "大宁金茂府距大宁国际小学127米学区优势明显，慧芝湖花园254米"
        },
        {
          "content": "两房源商业医疗配套相当，均享受大宁板块成熟配套资源"
        }
      ]
    },
    {
      "serial": "4",
      "type": "TITLE",
      "style": "SECTION",
      "title": "市场成交对比"
    },
    {
      "serial": "4.1",
      "type": "CHART",
      "style": "BAR",
      "title": "小区近12个月成交套数(套)",
      "cols": ["2024/08", "2024/09", "2024/10", "2024/11", "2024/12", "2025/01", "2025/02", "2025/03", "2025/04", "2025/05", "2025/06", "2025/07"],
      "content": [
        {
          "title": "小区A成交套数",
          "content": [数值1, 数值2, 数值3, 数值4, 数值5, 数值6, 数值7, 数值8, 数值9, 数值10, 数值11, 数值12]
        },
        {
          "title": "小区B成交套数",
          "content": [数值1, 数值2, 数值3, 数值4, 数值5, 数值6, 数值7, 数值8, 数值9, 数值10, 数值11, 数值12]
        }
      ]
    },
    {
      "serial": "4.2",
      "type": "CHART",
      "style": "BAR",
      "title": "小区近12个月成交均价(万元/㎡)",
      "cols": ["2024/08", "2024/09", "2024/10", "2024/11", "2024/12", "2025/01", "2025/02", "2025/03", "2025/04", "2025/05", "2025/06", "2025/07"],
      "content": [
        {
          "title": "小区A成交均价",
          "content": [数值1, 数值2, 数值3, 数值4, 数值5, 数值6, 数值7, 数值8, 数值9, 数值10, 数值11, 数值12]
        },
        {
          "title": "小区B成交均价",
          "content": [数值1, 数值2, 数值3, 数值4, 数值5, 数值6, 数值7, 数值8, 数值9, 数值10, 数值11, 数值12]
        }
      ]
    },
    {
      "serial": "4.3",
      "type": "CHART",
      "style": "MIXED",
      "title": "板块市场走势对比",
      "cols": ["2024/08", "2024/09", "2024/10", "2024/11", "2024/12", "2025/01", "2025/02", "2025/03", "2025/04", "2025/05", "2025/06", "2025/07"],
      "content": [
        {
          "title": "板块成交均价(万元/㎡)",
          "content": [数值1, 数值2, 数值3, 数值4, 数值5, 数值6, 数值7, 数值8, 数值9, 数值10, 数值11, 数值12],
          "chartType": "BAR"
        },
        {
          "title": "板块挂牌均价(万元/㎡)",
          "content": [数值1, 数值2, 数值3, 数值4, 数值5, 数值6, 数值7, 数值8, 数值9, 数值10, 数值11, 数值12],
          "chartType": "BAR"
        }
      ]
    },
    {
      "serial": "4.4",
      "type": "TABLE",
      "style": "NORMAL",
      "title": "当前市场数据对比",
      "cols": [
        "成交参数",
        "小区A",
        "小区B"
      ],
      "content": [
        [
          {"type": "TEXT", "content": "最新成交均价"},
          {"type": "TEXT", "content": "XXX元/㎡"},
          {"type": "TEXT", "content": "XXX元/㎡", "recommended": true}
        ],
        [
          {"type": "TEXT", "content": "最新挂牌均价"},
          {"type": "TEXT", "content": "XXX元/㎡", "recommended": true},
          {"type": "TEXT", "content": "XXX元/㎡"}
        ],
        [
          {"type": "TEXT", "content": "目前在售套数"},
          {"type": "TEXT", "content": "XX套"},
          {"type": "TEXT", "content": "XX套"}
        ],
        [
          {"type": "TEXT", "content": "在售降价套数"},
          {"type": "TEXT", "content": "XX套"},
          {"type": "TEXT", "content": "XX套"}
        ]
      ]
    },
    {
      "serial": "4.5",
      "type": "LIST",
      "style": "BOARD",
      "title": "市场分析结论",
      "content": [
        {
          "title": "价格趋势",
          "content": "价格走势分析要点"
        },
        {
          "title": "成交活跃度",
          "content": "成交量分析要点"
        },
        {
          "title": "投资建议",
          "content": "市场投资建议"
        }
      ]
    },
    {
      "serial": "4.6",
      "type": "LIST",
      "style": "BULLET",
      "title": "市场表现对比分析",
      "content": [
        {
          "content": "慧芝湖花园12月成交6套活跃度高，大宁金茂府成交稀少流动性差"
        },
        {
          "content": "大宁金茂府3月成交价16.7万/㎡创新高，慧芝湖花园价格稳定9万/㎡"
        },
        {
          "content": "大宁金茂府挂牌价与成交价差2.77%议价空间小，慧芝湖花园9.42%"
        }
      ]
    },
    {
      "serial": "5",
      "type": "TITLE",
      "style": "SECTION",
      "title": "综合购买建议"
    },
    {
      "serial": "5.1",
      "type": "TEXT",
      "style": "BOARD",
      "content": "【综合购买建议】基于以上全面对比分析，综合考虑房源基础信息、小区品质、板块能级和市场表现等多个维度，为不同需求的购房者提供针对性建议：预算充足且注重品质的改善型客户建议选择XXX房源，享受更优的居住体验和品牌价值；追求性价比的刚需客户可考虑XXX房源，在满足基本居住需求的同时获得更好的投资回报。(≤150字)"
    },
    {
      "serial": "5.3",
      "type": "CARD",
      "style": "BROKER",
      "title": "置业顾问推荐",
      "fields": {
        "name": "姓名",
        "icon": "头像URL",
        "education": "学历",
        "experience": "服务年限",
        "serviceCategory": [
          "服务类别1",
          "服务类别2"
        ],
        "specialSkill": [
          "特色专长1",
          "特色专长2"
        ],
        "suggest": "[根据对比分析,从经纪人视角给出消费者投资建议,限制50字以内,要专业/客观/中肯/有参考价值]",
        "wechat": "微信图片url",
        "phone": "联系电话"
      }
    }
  ]
}
```

<----------------------------(house_flow)---------------------------->
=
### 房源1详情
```json
${toJSONStr(request.houseCompareA)} 
```

### 房源2详情
```json
${toJSONStr(request.houseCompareB)}
```

<----------------------------(house)---------------------------->
=
### 房源1详情

```json
{
    "buildingArea": 109.00,
    "type": "SALE",
    "id": 53502,
    "roomCount": 3,
    "priceUnit": 137614.68,
    "townName": "大宁路街道",
    "boutique": false,
    "community": {
      "busiName": "凉城",
      "cityCode": "310100",
      "latitude": 31.286682,
      "typeName": "商务住宅;住宅区;住宅小区",
      "cityId": 310100,
      "detail": {
        "buildNum": 44,
        "blockName": "大宁",
        "powerdDesc": "民电",
        "setParkingFee": "1000",
        "parkingNum": 1818,
        "gasDesc": "3元/m³",
        "parkingRate": "1:1.2",
        "propertyType": "住宅",
        "id": 106107,
        "propertyFee": "5.05-6元/月/㎡",
        "upParkingNum": 0,
        "propertyYears": "70",
        "heatingDesc": "集中供暖",
        "downParkingNum": 0,
        "updateTime": "2025-01-05 10:29:38",
        "version": 1,
        "volumeRate": 2.20,
        "developerCorp": "方兴置业(上海)有限公司",
        "greenRate": 0.36,
        "homeNameFlag": true,
        "createTime": "2024-12-31 14:59:32",
        "propertyName": "中化金茂物业管理有限公司",
        "blockCd": "310106106",
        "buildMaxYear": "2020",
        "commBelong": "商品房",
        "waterDesc": "民水",
        "propertyPhone": "63562700、59369000",
        "houseNum": 1759,
        "buildMinYear": "2014",
        "buildingType": "板楼"
      },
      "around": {
        "bus": [
          {
            "typecode": "150700",
            "address": "312路;322路;46路;741路;79路;849路;862路;951路;95路",
            "distance": 409.0,
            "name": "共和新路彭江路(公交站)",
            "location": "121.451775,31.284557",
            "id": "BV10025190",
            "type": "交通设施服务;公交车站;公交车站相关"
          }
        ],
        "kindergarten": [
          {
            "typecode": "141204",
            "address": "永和东路633号(近共和新路)",
            "distance": 414.0,
            "name": "大宁国际幼儿园(永和东路)",
            "location": "121.453884,31.290206",
            "id": "B00156NT8R",
            "type": "科教文化服务;学校;幼儿园"
          },
          {
            "typecode": "141204",
            "address": "灵石路236号",
            "distance": 417.0,
            "name": "大宁国际第二幼儿园(灵石路)",
            "location": "121.455405,31.282937",
            "id": "B00156NTHD",
            "type": "科教文化服务;学校;幼儿园"
          }
        ],
        "types": [
          "公交",
          "学校",
          "医院",
          "购物",
          "餐饮"
        ],
        "subway": [],
        "restaurant": [
          {
            "typecode": "050100",
            "address": "灵石路217号5楼1500室,6楼1600-1613室",
            "distance": 367.0,
            "name": "和记小菜(大宁店)",
            "location": "121.456549,31.283560",
            "id": "B0FFGDLJ3K",
            "type": "餐饮服务;中餐厅;中餐厅"
          },
          {
            "typecode": "050112",
            "address": "永和东路597二层",
            "distance": 390.0,
            "name": "潮厨鲜牛肉火锅店",
            "location": "121.454995,31.290180",
            "id": "B0H065HDS6",
            "type": "餐饮服务;中餐厅;湖北菜(鄂菜)"
          },
          {
            "typecode": "050101",
            "address": "共和新路2750号(锦荣国际大酒店二楼)",
            "distance": 522.0,
            "name": "百福私宴",
            "location": "121.450967,31.283779",
            "id": "B0HRSZPE9Q",
            "type": "餐饮服务;中餐厅;综合酒楼"
          },
          {
            "typecode": "050000",
            "address": "共和新路2750号锦荣国际大酒店1层",
            "distance": 528.0,
            "name": "俄罗斯飞象西餐厅",
            "location": "121.450883,31.283780",
            "id": "B0FFJHIRD0",
            "type": "餐饮服务;餐饮相关场所;餐饮相关"
          },
          {
            "typecode": "050100",
            "address": "共和新路3018号(汶水路地铁站3号口步行310米)",
            "distance": 552.0,
            "name": "共和海鲜面馆",
            "location": "121.450448,31.289421",
            "id": "B0K1GRYE3P",
            "type": "餐饮服务;中餐厅;中餐厅"
          },
          {
            "typecode": "050600",
            "address": "平型关路1588号2楼",
            "distance": 566.0,
            "name": "海上明茶院",
            "location": "121.458928,31.290702",
            "id": "B0FFLG68AN",
            "type": "餐饮服务;茶艺馆;茶艺馆"
          }
        ],
        "pharmacy": [
          {
            "typecode": "090602",
            "address": "永和东路483号203室",
            "distance": 469.0,
            "name": "正中脊(上海静安店)",
            "location": "121.456732,31.290712",
            "id": "B0KRKA18IY",
            "type": "医疗保健服务;医药保健销售店;医疗保健用品"
          }
        ],
        "primarySchool": [
          {
            "typecode": "141203",
            "address": "广延路908号(近彭江路)",
            "distance": 127.0,
            "name": "上海市大宁国际小学",
            "location": "121.454119,31.286120",
            "id": "B0FFGPUPWO",
            "type": "科教文化服务;学校;小学"
          }
        ],
        "hospital": [
          {
            "typecode": "090202",
            "address": "大宁路街道共和新路2768、2770号1楼",
            "distance": 455.0,
            "name": "乐牙口腔",
            "location": "121.451371,31.284332",
            "id": "B0G2U60ECS",
            "type": "医疗保健服务;专科医院;口腔医院"
          },
          {
            "typecode": "090100",
            "address": "平型关路1505号",
            "distance": 455.0,
            "name": "上海市静安区中医医院(平型关路院区)",
            "location": "121.459141,31.289107",
            "id": "B0JU3ZDIP3",
            "type": "医疗保健服务;综合医院;综合医院"
          },
          {
            "typecode": "090202",
            "address": "永和东路439-443二层A区447-451号二层",
            "distance": 525.0,
            "name": "正毅口腔",
            "location": "121.457622,31.290960",
            "id": "B0HDALXSFE",
            "type": "医疗保健服务;专科医院;口腔医院"
          }
        ],
        "middleSchool": [
          {
            "typecode": "141202",
            "address": "共和新路2800号",
            "distance": 486.0,
            "name": "上海市风华初级中学(北校区)",
            "location": "121.450979,31.284323",
            "id": "B0FFFVAG58",
            "type": "科教文化服务;学校;中学"
          }
        ],
        "shopping": [
          {
            "typecode": "060603",
            "address": "共和新路3024号(汶水路地铁站3号口步行300米)",
            "distance": 553.0,
            "name": "佳品五金商店",
            "location": "121.450496,31.289508",
            "id": "B0FFIDNNTR",
            "type": "购物服务;家居建材市场;建材五金市场"
          },
          {
            "typecode": "060704",
            "address": "灵石路500号(上海马戏城地铁站4号口步行370米)",
            "distance": 566.0,
            "name": "百果园(灵石路店)",
            "location": "121.451134,31.283030",
            "id": "B0FFHYAZLW",
            "type": "购物服务;综合市场;果品市场"
          }
        ]
      },
      "cityName": "上海市",
      "formattedAddress": "上海市静安区大宁路街道彭江路333号大宁金茂府",
      "id": 54915,
      "longitude": 121.455285,
      "townName": "大宁路街道",
      "address": "彭江路333号",
      "districtName": "静安区",
      "districtId": 310106,
      "name": "大宁金茂府",
      "location": "121.455285,31.286682",
      "provinceName": "上海市"
    },
    "labels": [
      "降价好房",
      "随时可看",
      "满五",
      "景观房",
      "看房有礼",
      "采光充足"
    ],
    "coverUrl": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/70a96df3-a479-43d1-818a-de032408869etmp_470f5a59f84909a6a99af91db192f8ec.jpg",
    "lookType": "随时可看",
    "channelDelegationMap": {
      "ALIPAY": {
        "businessQuantity": 100,
        "outNickName": "特务小鹤",
        "freeze": false,
        "remainQuantity": 49,
        "status": "WITHOUT"
      },
      "XIANYU": {
        "businessQuantity": 0,
        "outNickName": "特务小鹤",
        "freeze": false,
        "remainQuantity": 0,
        "status": "WITHOUT"
      },
      "PRIVATE": {
        "code": "20342be1044449cb86c75e969cc4e1e9",
        "freeze": false,
        "remark": "",
        "id": 53503,
        "status": "UP_SUCC"
      }
    },
    "metro": "3",
    "toiletCount": 1,
    "hallCount": 2,
    "subType": "SALE_FULL",
    "currentFloor": 8,
    "brokerName": "戴阳",
    "status": "INIT",
    "listDate": "2025-07-25",
    "subTypeName": "二手整售",
    "typeName": "二手房",
    "communityAddress": "上海市静安区彭江路333号",
    "floorCategory": "低楼层",
    "priceTotal": 1500.00,
    "deadline": "2025-10-25 10:30:26",
    "parkingRatio": "2",
    "buildName": "1栋",
    "districtName": "静安区",
    "companyIdStr": "9095074090737248256",
    "broker": {
      "professionInformationCardUrl": "https://fyoss-test.fangyou.com/24122310acd217f1fc132c40bbc5be433a5d66620f019954.jpg",
      "code": "9094662523348608768",
      "name": "戴阳",
      "icon": "https://fyoss-test.fangyou.com/jpg_24110814a0ec060e52b6dcbdd8efa91ca2bbb35fee07fb54",
      "company": "esigntest上海添玑好房网络服务有限公司PABK",
      "storeName": "测试",
      "id": "9094662523348608768",
      "professionInformationCardNumber": "147288"
    },
    "mediasMap": {
      "实景图": [
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/70a96df3-a479-43d1-818a-de032408869etmp_470f5a59f84909a6a99af91db192f8ec.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/7c1f883b-f535-4915-9347-9d3abca511catmp_0ea23124837ec6569655358c3981e418.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/a378c052-bcda-4b78-b5e0-d6e6f1c7d35btmp_ffd05b3bd8220c0e63bde8bb3cb5444e.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/1ddaa424-4940-485f-ad10-6194ec7c8712tmp_f351e085459f573c312de35f6edf2fe4.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/3eb4cdb1-dab0-48d4-8302-7992ea06a293tmp_e8248d2d9eefcbdda950a219a8ea0aa5.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/460cfdd6-d294-48ec-b54e-44d8b903584atmp_24e48c526657672e2230674bee6afe3c.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/5685e98e-ad5f-4c19-b241-59d200a6329atmp_a63d7796657a4bb45ca091aa9129c6d7.jpg"
      ]
    },
    "equipments": [],
    "houseCertVerify": true,
    "tagElevator": true,
    "brokerService": [],
    "busiName": "凉城",
    "layoutName": "3室2厅1卫",
    "completionTime": "2020",
    "roomNum": "101",
    "school": "有",
    "housePlanPurpose": "住宅",
    "communityName": "大宁金茂府",
    "unitName": "1单元",
    "orient": "朝南",
    "level": "BROKER",
    "medias": [
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/70a96df3-a479-43d1-818a-de032408869etmp_470f5a59f84909a6a99af91db192f8ec.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/7c1f883b-f535-4915-9347-9d3abca511catmp_0ea23124837ec6569655358c3981e418.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/a378c052-bcda-4b78-b5e0-d6e6f1c7d35btmp_ffd05b3bd8220c0e63bde8bb3cb5444e.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/1ddaa424-4940-485f-ad10-6194ec7c8712tmp_f351e085459f573c312de35f6edf2fe4.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/3eb4cdb1-dab0-48d4-8302-7992ea06a293tmp_e8248d2d9eefcbdda950a219a8ea0aa5.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/460cfdd6-d294-48ec-b54e-44d8b903584atmp_24e48c526657672e2230674bee6afe3c.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251029/5685e98e-ad5f-4c19-b241-59d200a6329atmp_a63d7796657a4bb45ca091aa9129c6d7.jpg"
      }
    ],
    "companyId": 9095074090737248256,
    "totalFloor": 30,
    "code": "99d054f13d274312ae949dc2b32702a2",
    "remark": "",
    "redo": "豪华",
    "title": "大宁金茂府 3室2厅1卫 109平 豪华",
    "around": [
      "公交",
      "学校",
      "医院",
      "购物",
      "餐饮"
    ],
    "cityName": "上海市",
    "propertyType": "住宅",
    "propertyYears": "70",
    "houseType": "商品房",
    "propertyManagementCompany": "中化金茂物业管理有限公司"
}
```

### 房源2详情

```json
{
    "buildingArea": 102.00,
    "type": "SALE",
    "id": 53504,
    "roomCount": 2,
    "priceUnit": 127450.98,
    "townName": "大宁路街道",
    "boutique": false,
    "community": {
      "busiName": "凉城",
      "cityCode": "310100",
      "latitude": 31.281544,
      "typeName": "商务住宅;住宅区;住宅小区",
      "cityId": 310100,
      "around": {
        "bus": [
          {
            "typecode": "150700",
            "address": "107路;547路;767路;79路;858路;862路;912路;944路",
            "distance": 305.0,
            "name": "广中路平型关路(公交站)",
            "location": "121.456897,31.279319",
            "id": "BV10025188",
            "type": "交通设施服务;公交车站;公交车站相关"
          }
        ],
        "kindergarten": [
          {
            "typecode": "141204",
            "address": "灵石路236号",
            "distance": 355.0,
            "name": "大宁国际第二幼儿园(灵石路)",
            "location": "121.455405,31.282937",
            "id": "B00156NTHD",
            "type": "科教文化服务;学校;幼儿园"
          }
        ],
        "types": [
          "公交",
          "学校",
          "医院",
          "购物",
          "餐饮"
        ],
        "subway": [],
        "restaurant": [
          {
            "typecode": "050501",
            "address": "广中路765号771号嘉悦会1层",
            "distance": 199.0,
            "name": "星巴克(上海慧芝湖店)",
            "location": "121.457972,31.279887",
            "id": "B0FFM45HIF",
            "type": "餐饮服务;咖啡厅;星巴克咖啡"
          },
          {
            "typecode": "050100",
            "address": "灵石路217号5楼1500室,6楼1600-1613室",
            "distance": 308.0,
            "name": "和记小菜(大宁店)",
            "location": "121.456549,31.283560",
            "id": "B0FFGDLJ3K",
            "type": "餐饮服务;中餐厅;中餐厅"
          },
          {
            "typecode": "050000",
            "address": "广中路657号8幢",
            "distance": 376.0,
            "name": "有家川菜(广中店)",
            "location": "121.462425,31.280244",
            "id": "B0FFK8AQY7",
            "type": "餐饮服务;餐饮相关场所;餐饮相关"
          },
          {
            "typecode": "050300",
            "address": "北宝兴路624号百联莘荟购物中心F1层",
            "distance": 532.0,
            "name": "德克士(百联莘荟购物中心店)",
            "location": "121.461607,31.277424",
            "id": "B0H3LDJL0J",
            "type": "餐饮服务;快餐厅;快餐厅"
          },
          {
            "typecode": "050500",
            "address": "北宝兴路624号F101+F102室",
            "distance": 532.0,
            "name": "Tims天好咖啡(百联莘荟购物中心店)",
            "location": "121.461579,31.277400",
            "id": "B0HA9N7DGB",
            "type": "餐饮服务;咖啡厅;咖啡厅"
          },
          {
            "typecode": "050000",
            "address": "北宝兴路624号院内F独栋",
            "distance": 542.0,
            "name": "颐和国际公馆.江浙精致私房菜(百联莘荟购物中心店)",
            "location": "121.460847,31.277002",
            "id": "B0I215OZEX",
            "type": "餐饮服务;餐饮相关场所;餐饮相关"
          }
        ],
        "pharmacy": [
          {
            "typecode": "090601",
            "address": "平型关路992-996号",
            "distance": 166.0,
            "name": "益丰大药房(平型关路店)",
            "location": "121.457970,31.280217",
            "id": "B0FFJACO2B",
            "type": "医疗保健服务;医药保健销售店;药房"
          }
        ],
        "primarySchool": [
          {
            "typecode": "141203",
            "address": "北宝兴路900号(北宝兴路灵石路)",
            "distance": 254.0,
            "name": "上海市大宁国际小学",
            "location": "121.460633,31.283192",
            "id": "B00155G7IZ",
            "type": "科教文化服务;学校;小学"
          },
          {
            "typecode": "141203",
            "address": "大宁路181弄15号(上海马戏城地铁站2号口步行380米)",
            "distance": 518.0,
            "name": "上海市静安区大宁路小学",
            "location": "121.455184,31.278038",
            "id": "B00156OCZV",
            "type": "科教文化服务;学校;小学"
          }
        ],
        "hospital": [
          {
            "typecode": "090202",
            "address": "广中路865号(近马戏城地铁站)",
            "distance": 348.0,
            "name": "登特口腔",
            "location": "121.455707,31.279827",
            "id": "B00156NTH9",
            "type": "医疗保健服务;专科医院;口腔医院"
          },
          {
            "typecode": "090000",
            "address": "北宝兴路624号17栋F201-202室",
            "distance": 500.0,
            "name": "上海赞瞳眼科诊所",
            "location": "121.461121,31.277523",
            "id": "B0JB4HZSHP",
            "type": "医疗保健服务;医疗保健服务场所;医疗保健服务场所"
          }
        ],
        "middleSchool": [],
        "shopping": [
          {
            "typecode": "060704",
            "address": "平型关路1063号-2",
            "distance": 56.0,
            "name": "百果园(慧芝湖花园店)",
            "location": "121.458218,31.281372",
            "id": "B0FFGHKE9V",
            "type": "购物服务;综合市场;果品市场"
          },
          {
            "typecode": "060900",
            "address": "广中路909号(上海马戏城地铁站1号口步行220米)",
            "distance": 433.0,
            "name": "自然运动·普拉提(宝华店)",
            "location": "121.454650,31.279880",
            "id": "B0JACLVBU1",
            "type": "购物服务;体育用品店;体育用品店"
          },
          {
            "typecode": "060000",
            "address": "北宝兴路624号百联莘荟购物中心F1层",
            "distance": 500.0,
            "name": "盒马奥莱(百联莘荟购物中心店)",
            "location": "121.460966,31.277459",
            "id": "B0JKZHNEJC",
            "type": "购物服务;购物相关场所;购物相关场所"
          },
          {
            "typecode": "060101|060102",
            "address": "北宝兴路624号",
            "distance": 500.0,
            "name": "百联莘荟购物中心",
            "location": "121.460966,31.277459",
            "id": "B0FFMGE5SZ",
            "type": "购物服务;商场;购物中心|购物服务;商场;普通商场"
          },
          {
            "typecode": "060706",
            "address": "共和新路2449号泛欧现代大厦1413室",
            "distance": 587.0,
            "name": "宫品海参(静安大宁专卖店)",
            "location": "121.452704,31.280586",
            "id": "B00156ZA8K",
            "type": "购物服务;综合市场;水产海鲜市场"
          }
        ]
      },
      "cityName": "上海市",
      "formattedAddress": "上海市静安区大宁路街道慧芝湖花园",
      "detail" : {
        "buildNum" : 9,
        "blockName" : "大宁",
        "powerdDesc" : "民电",
        "setParkingFee" : "300",
        "parkingNum" : 3494,
        "gasDesc" : "3元/m³",
        "parkingRate" : "1.00:0.70",
        "propertyType" : "住宅",
        "id" : 129712,
        "propertyFee" : "2.7元/月/㎡",
        "upParkingNum" : 0,
        "propertyYears" : "50/70",
        "heatingDesc" : "自采暖",
        "downParkingNum" : 0,
        "updateTime" : "2025-01-09 15:11:45",
        "version" : 2,
        "volumeRate" : 2.50,
        "developerCorp" : "嘉华(中国)投资有限公司",
        "greenRate" : 0.45,
        "createTime" : "2024-12-31 15:00:49",
        "propertyName" : "龙湖物业",
        "blockCd" : "310106106",
        "buildMaxYear" : "2009",
        "commBelong" : "商品房/使用权",
        "waterDesc" : "民水",
        "propertyPhone" : "021-66525123",
        "houseNum" : 3526,
        "buildMinYear" : "2004",
        "buildingType" : "板楼"
      },
      "id": 1,
      "longitude": 121.458773,
      "townName": "大宁路街道",
      "address": "平型关路1083弄",
      "districtName": "静安区",
      "districtId": 310106,
      "name": "慧芝湖花园",
      "location": "121.458773,31.281544",
      "provinceName": "上海市"
    },
    "labels": [
      "满五",
      "精装修",
      "拎包入住",
      "南北通透",
      "明厨明卫",
      "送家具家电",
      "近地铁"
    ],
    "coverUrl": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/c285b8de-2e63-49e8-afd6-71310753b216tmp_890416670c21d2d9f423e23c8f2a9d78.jpg",
    "lookType": "随时可看",
    "channelDelegationMap": {
      "ALIPAY": {
        "businessQuantity": 100,
        "outNickName": "特务小鹤",
        "freeze": false,
        "remainQuantity": 49,
        "status": "WITHOUT"
      },
      "XIANYU": {
        "businessQuantity": 0,
        "outNickName": "特务小鹤",
        "freeze": false,
        "remainQuantity": 0,
        "status": "WITHOUT"
      },
      "PRIVATE": {
        "code": "0f3efb16b60c472a9c34a884b8aa6602",
        "freeze": false,
        "remark": "",
        "id": 53505,
        "status": "UP_SUCC"
      }
    },
    "metro": "1",
    "toiletCount": 1,
    "hallCount": 1,
    "subType": "SALE_FULL",
    "currentFloor": 12,
    "brokerName": "戴阳",
    "status": "INIT",
    "listDate": "2025-07-25",
    "subTypeName": "二手整售",
    "typeName": "二手房",
    "communityAddress": "上海市静安区平型关路1083弄",
    "floorCategory": "中楼层",
    "priceTotal": 1300.00,
    "deadline": "2025-10-25 10:32:10",
    "parkingRatio": "1.00:0.70",
    "buildName": "1栋",
    "districtName": "静安区",
    "companyIdStr": "9095074090737248256",
    "broker": {
      "professionInformationCardUrl": "https://fyoss-test.fangyou.com/24122310acd217f1fc132c40bbc5be433a5d66620f019954.jpg",
      "code": "9094662523348608768",
      "name": "戴阳",
      "icon": "https://fyoss-test.fangyou.com/jpg_24110814a0ec060e52b6dcbdd8efa91ca2bbb35fee07fb54",
      "company": "esigntest上海添玑好房网络服务有限公司PABK",
      "storeName": "测试",
      "id": "9094662523348608768",
      "professionInformationCardNumber": "147288"
    },
    "mediasMap": {
      "实景图": [
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/c285b8de-2e63-49e8-afd6-71310753b216tmp_890416670c21d2d9f423e23c8f2a9d78.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/182dc8d1-c7bb-4604-9aa4-7ebe48af61eetmp_347cc8a3fafd19b3c2a08d8124b9afca.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/7f6356b5-5017-4ac3-9733-75308e93d42atmp_5c7324a937586c5db39763530c46a5c6.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/38d5b074-34b5-44c5-b3b6-9c5813fd893atmp_b3e93d94bf03bc2c3f52b80a50828e33.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/4057dbef-f90c-4821-a61c-e196b6985dd1tmp_f6d1fb7bde64cfd5b783dfb21c134821.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/526454a8-24a6-4f3f-bd0d-eb052982f544tmp_6ced99dd822fbb1dee67600a68201618.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/aff42de7-e290-4c53-8f02-e71e96dbc0cctmp_61949b2846a6249b154a88ccc7dbdcac.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/6f7d3fd1-b3d7-4fb4-84d0-3b2a44a01631tmp_a801533d3cc9a6e219a6ea4ddcc19458.jpg",
        "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/1772536a-599e-451b-9a1f-7fca9e5754b0tmp_6daa74e6d9a0e679a31404a61369ef34.jpg"
      ]
    },
    "equipments": [],
    "houseCertVerify": true,
    "tagElevator": true,
    "brokerService": [],
    "busiName": "凉城",
    "layoutName": "2室1厅1卫",
    "completionTime": "2019",
    "roomNum": "101",
    "school": "有",
    "housePlanPurpose": "住宅",
    "communityName": "慧芝湖花园",
    "unitName": "1单元",
    "orient": "朝南",
    "level": "BROKER",
    "medias": [
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/c285b8de-2e63-49e8-afd6-71310753b216tmp_890416670c21d2d9f423e23c8f2a9d78.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/182dc8d1-c7bb-4604-9aa4-7ebe48af61eetmp_347cc8a3fafd19b3c2a08d8124b9afca.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/7f6356b5-5017-4ac3-9733-75308e93d42atmp_5c7324a937586c5db39763530c46a5c6.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/38d5b074-34b5-44c5-b3b6-9c5813fd893atmp_b3e93d94bf03bc2c3f52b80a50828e33.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/4057dbef-f90c-4821-a61c-e196b6985dd1tmp_f6d1fb7bde64cfd5b783dfb21c134821.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/526454a8-24a6-4f3f-bd0d-eb052982f544tmp_6ced99dd822fbb1dee67600a68201618.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/aff42de7-e290-4c53-8f02-e71e96dbc0cctmp_61949b2846a6249b154a88ccc7dbdcac.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/6f7d3fd1-b3d7-4fb4-84d0-3b2a44a01631tmp_a801533d3cc9a6e219a6ea4ddcc19458.jpg"
      },
      {
        "subtype": "实景图",
        "type": "IMAGE",
        "url": "https://etc-consumer.oss-cn-shanghai.aliyuncs.com/delegation-media/9094662523348608768/202507251030/1772536a-599e-451b-9a1f-7fca9e5754b0tmp_6daa74e6d9a0e679a31404a61369ef34.jpg"
      }
    ],
    "companyId": 9095074090737248256,
    "totalFloor": 30,
    "code": "07d081bd168945a5a1664e53fba04426",
    "remark": "",
    "redo": "豪华",
    "title": "慧芝湖花园 2室1厅1卫 102平 豪华",
    "around": [
      "公交",
      "学校",
      "医院",
      "购物",
      "餐饮"
    ],
    "cityName": "上海市",
    "propertyType": "住宅",
    "propertyYears": "70",
    "houseType": "商品房",
    "propertyManagementCompany": "龙湖物业"
}
```


<----------------------------(cric_output_flow)---------------------------->
=
${prevGenerateResponse}

<----------------------------(cric_output)---------------------------->
=


### 小区交易数据对比

```json
{
    "compare": "\n| 板块核心参数         | 一品漫城（一期）                                                                 | 浦江华侨城（三期）                                                                 |\n|----------------------|----------------------------------------------------------------------------------|------------------------------------------------------------------------------------|\n| 所属板块         | 浦锦街道                                                                         | 浦江镇                                                                             |\n| 地铁             | 8号线芦恒路站（245m）<br/>**受周边动迁房影响，本站早晚高峰拥挤程度高**           | 8号线浦江镇站（550m）<br/>因距离终点站更近，本站早晚高峰拥挤程度尚可               |\n| 拥堵情况         | 早高峰浦星路拥堵严重<br/>去前滩5公里常堵30分钟                                   | 同样拥堵，本小区更近济阳路高架入口                                                 |\n| 交通利好         | 银都路隧道已建成（10分钟至浦西），机场联络线三林南站（在建）     | **在建19号线（浦锦路站）将分流，步行可达，可提升小区交通便利，未来存在房价上升潜力** |                                                                         |                                                                                    |\n| 大型商场         | 东方懿德城（1.3km）                                                              | O'MALL华侨城中心（300m）                                                           |\n| 超市            | 联华超市（285m）                                                                 | 家乐福（280m）                                                                     |\n| 学校             | 上南实验小学（公立816m）<br/>**有消息称，对口学校将改为上师大附属，小区未来溢价潜力大** | 上师大三附小（公立1.4km）                                                          |\n| 医院             | 仁济南院（3.7km，三甲）                                                          | 仁济南院（1.5km，三甲）                                                            |\n",
    "sellingData": {
      "慧芝湖花园（一二期）": {
        "所在小区近12个月走势": [
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 0,
            "成交均价环比(%)": -84.58,
            "月度": "2024年08月",
            "挂牌均价(元/m²)": 0,
            "新增挂牌面积(m²)": 0,
            "成交套数(套)": 1,
            "挂牌均价环比(%)": 0,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 34,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 17059
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 0,
            "成交均价环比(%)": 0,
            "月度": "2024年09月",
            "挂牌均价(元/m²)": 0,
            "新增挂牌面积(m²)": 0,
            "成交套数(套)": 0,
            "挂牌均价环比(%)": 0,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 0,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 0
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 3,
            "成交均价环比(%)": 0,
            "月度": "2024年10月",
            "挂牌均价(元/m²)": 100000,
            "新增挂牌面积(m²)": 417,
            "成交套数(套)": 1,
            "挂牌均价环比(%)": 0,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 87,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 96437
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 5,
            "成交均价环比(%)": -5.51,
            "月度": "2024年11月",
            "挂牌均价(元/m²)": 106473,
            "新增挂牌面积(m²)": 482,
            "成交套数(套)": 3,
            "挂牌均价环比(%)": 6.47,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 357,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 91120
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 7,
            "成交均价环比(%)": 0.94,
            "月度": "2024年12月",
            "挂牌均价(元/m²)": 105950,
            "新增挂牌面积(m²)": 763,
            "成交套数(套)": 6,
            "挂牌均价环比(%)": -0.49,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 556,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 91973
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 2,
            "成交均价环比(%)": 5.02,
            "月度": "2025年01月",
            "挂牌均价(元/m²)": 102416,
            "新增挂牌面积(m²)": 178,
            "成交套数(套)": 1,
            "挂牌均价环比(%)": -3.34,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 88,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 96591
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 7,
            "成交均价环比(%)": -23.49,
            "月度": "2025年02月",
            "挂牌均价(元/m²)": 101960,
            "新增挂牌面积(m²)": 903,
            "成交套数(套)": 2,
            "挂牌均价环比(%)": -0.45,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 123,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 73902
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 10,
            "成交均价环比(%)": 26.08,
            "月度": "2025年03月",
            "挂牌均价(元/m²)": 109001,
            "新增挂牌面积(m²)": 1201,
            "成交套数(套)": 2,
            "挂牌均价环比(%)": 6.91,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 296,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 93176
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 2,
            "成交均价环比(%)": 1.15,
            "月度": "2025年04月",
            "挂牌均价(元/m²)": 108324,
            "新增挂牌面积(m²)": 179,
            "成交套数(套)": 1,
            "挂牌均价环比(%)": -0.62,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 73,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 94247
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 4,
            "成交均价环比(%)": -8.88,
            "月度": "2025年05月",
            "挂牌均价(元/m²)": 107222,
            "新增挂牌面积(m²)": 468,
            "成交套数(套)": 3,
            "挂牌均价环比(%)": -1.02,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 238,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 85882
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 6,
            "成交均价环比(%)": 0,
            "月度": "2025年06月",
            "挂牌均价(元/m²)": 103070,
            "新增挂牌面积(m²)": 645,
            "成交套数(套)": 0,
            "挂牌均价环比(%)": -3.87,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 0,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 0
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 4,
            "成交均价环比(%)": 0,
            "月度": "2025年07月",
            "挂牌均价(元/m²)": 105689,
            "新增挂牌面积(m²)": 559,
            "成交套数(套)": 0,
            "挂牌均价环比(%)": 0,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 0,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 0
          }
        ],
        "所在板块近12个月走势": [
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 153,
            "成交均价环比(%)": -12.09,
            "月度": "2024年08月",
            "挂牌均价(元/m²)": 78913,
            "新增挂牌面积(m²)": 12084,
            "成交套数(套)": 28,
            "挂牌均价环比(%)": -0.22,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 1978,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 72456
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 173,
            "成交均价环比(%)": 5.76,
            "月度": "2024年09月",
            "挂牌均价(元/m²)": 82594,
            "新增挂牌面积(m²)": 14040,
            "成交套数(套)": 31,
            "挂牌均价环比(%)": 4.66,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 2305,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 76633
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 203,
            "成交均价环比(%)": 1.49,
            "月度": "2024年10月",
            "挂牌均价(元/m²)": 82346,
            "新增挂牌面积(m²)": 17548,
            "成交套数(套)": 47,
            "挂牌均价环比(%)": -0.3,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 3519,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 77774
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 191,
            "成交均价环比(%)": 2.2,
            "月度": "2024年11月",
            "挂牌均价(元/m²)": 82061,
            "新增挂牌面积(m²)": 16101,
            "成交套数(套)": 63,
            "挂牌均价环比(%)": -0.35,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 4917,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 79483
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 175,
            "成交均价环比(%)": 2.76,
            "月度": "2024年12月",
            "挂牌均价(元/m²)": 80577,
            "新增挂牌面积(m²)": 13939,
            "成交套数(套)": 72,
            "挂牌均价环比(%)": -1.81,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 5804,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 81676
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 90,
            "成交均价环比(%)": -2.23,
            "月度": "2025年01月",
            "挂牌均价(元/m²)": 77387,
            "新增挂牌面积(m²)": 7322,
            "成交套数(套)": 34,
            "挂牌均价环比(%)": -3.96,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 2889,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 79855
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 217,
            "成交均价环比(%)": -12.49,
            "月度": "2025年02月",
            "挂牌均价(元/m²)": 80282,
            "新增挂牌面积(m²)": 18538,
            "成交套数(套)": 22,
            "挂牌均价环比(%)": 3.74,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 1402,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 69882
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 226,
            "成交均价环比(%)": 7.29,
            "月度": "2025年03月",
            "挂牌均价(元/m²)": 81956,
            "新增挂牌面积(m²)": 19118,
            "成交套数(套)": 82,
            "挂牌均价环比(%)": 2.09,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 6573,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 74976
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 173,
            "成交均价环比(%)": -7.37,
            "月度": "2025年04月",
            "挂牌均价(元/m²)": 78560,
            "新增挂牌面积(m²)": 14109,
            "成交套数(套)": 49,
            "挂牌均价环比(%)": -4.14,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 3349,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 69449
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 190,
            "成交均价环比(%)": 2.89,
            "月度": "2025年05月",
            "挂牌均价(元/m²)": 79206,
            "新增挂牌面积(m²)": 15946,
            "成交套数(套)": 50,
            "挂牌均价环比(%)": 0.82,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 3688,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 71457
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 172,
            "成交均价环比(%)": 4.39,
            "月度": "2025年06月",
            "挂牌均价(元/m²)": 78951,
            "新增挂牌面积(m²)": 15655,
            "成交套数(套)": 30,
            "挂牌均价环比(%)": -0.32,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 2369,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 74596
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 108,
            "成交均价环比(%)": 0,
            "月度": "2025年07月",
            "挂牌均价(元/m²)": 76071,
            "新增挂牌面积(m²)": 10025,
            "成交套数(套)": 4,
            "挂牌均价环比(%)": 0,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 356,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 60253
          }
        ]
      },
      "大宁金茂府": {
        "所在小区近12个月走势": [
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 2,
            "成交均价环比(%)": 0,
            "月度": "2024年08月",
            "挂牌均价(元/m²)": 147525,
            "新增挂牌面积(m²)": 202,
            "成交套数(套)": 0,
            "挂牌均价环比(%)": -9.18,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 0,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 0
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 4,
            "成交均价环比(%)": 0,
            "月度": "2024年09月",
            "挂牌均价(元/m²)": 167770,
            "新增挂牌面积(m²)": 574,
            "成交套数(套)": 1,
            "挂牌均价环比(%)": 13.72,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 108,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 147037
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 4,
            "成交均价环比(%)": -2.38,
            "月度": "2024年10月",
            "挂牌均价(元/m²)": 148681,
            "新增挂牌面积(m²)": 523,
            "成交套数(套)": 1,
            "挂牌均价环比(%)": -11.38,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 96,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 143542
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 4,
            "成交均价环比(%)": 0,
            "月度": "2024年11月",
            "挂牌均价(元/m²)": 163741,
            "新增挂牌面积(m²)": 687,
            "成交套数(套)": 0,
            "挂牌均价环比(%)": 10.13,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 0,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 0
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 1,
            "成交均价环比(%)": 0,
            "月度": "2024年12月",
            "挂牌均价(元/m²)": 152663,
            "新增挂牌面积(m²)": 169,
            "成交套数(套)": 1,
            "挂牌均价环比(%)": -6.77,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 157,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 122293
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 1,
            "成交均价环比(%)": 0,
            "月度": "2025年01月",
            "挂牌均价(元/m²)": 165741,
            "新增挂牌面积(m²)": 108,
            "成交套数(套)": 0,
            "挂牌均价环比(%)": 8.57,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 0,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 0
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 0,
            "成交均价环比(%)": 0,
            "月度": "2025年02月",
            "挂牌均价(元/m²)": 0,
            "新增挂牌面积(m²)": 0,
            "成交套数(套)": 0,
            "挂牌均价环比(%)": 0,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 0,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 0
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 0,
            "成交均价环比(%)": 0,
            "月度": "2025年03月",
            "挂牌均价(元/m²)": 0,
            "新增挂牌面积(m²)": 0,
            "成交套数(套)": 1,
            "挂牌均价环比(%)": 0,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 239,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 167364
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 0,
            "成交均价环比(%)": 0,
            "月度": "2025年04月",
            "挂牌均价(元/m²)": 0,
            "新增挂牌面积(m²)": 0,
            "成交套数(套)": 0,
            "挂牌均价环比(%)": 0,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 0,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 0
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 0,
            "成交均价环比(%)": 0,
            "月度": "2025年05月",
            "挂牌均价(元/m²)": 0,
            "新增挂牌面积(m²)": 0,
            "成交套数(套)": 0,
            "挂牌均价环比(%)": 0,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 0,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 0
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 0,
            "成交均价环比(%)": 0,
            "月度": "2025年06月",
            "挂牌均价(元/m²)": 0,
            "新增挂牌面积(m²)": 0,
            "成交套数(套)": 0,
            "挂牌均价环比(%)": 0,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 0,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 0
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 0,
            "成交均价环比(%)": 0,
            "月度": "2025年07月",
            "挂牌均价(元/m²)": 0,
            "新增挂牌面积(m²)": 0,
            "成交套数(套)": 0,
            "挂牌均价环比(%)": 0,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 0,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 0
          }
        ],
        "所在板块近12个月走势": [
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 153,
            "成交均价环比(%)": -12.09,
            "月度": "2024年08月",
            "挂牌均价(元/m²)": 78913,
            "新增挂牌面积(m²)": 12084,
            "成交套数(套)": 28,
            "挂牌均价环比(%)": -0.22,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 1978,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 72456
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 173,
            "成交均价环比(%)": 5.76,
            "月度": "2024年09月",
            "挂牌均价(元/m²)": 82594,
            "新增挂牌面积(m²)": 14040,
            "成交套数(套)": 31,
            "挂牌均价环比(%)": 4.66,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 2305,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 76633
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 203,
            "成交均价环比(%)": 1.49,
            "月度": "2024年10月",
            "挂牌均价(元/m²)": 82346,
            "新增挂牌面积(m²)": 17548,
            "成交套数(套)": 47,
            "挂牌均价环比(%)": -0.3,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 3519,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 77774
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 191,
            "成交均价环比(%)": 2.2,
            "月度": "2024年11月",
            "挂牌均价(元/m²)": 82061,
            "新增挂牌面积(m²)": 16101,
            "成交套数(套)": 63,
            "挂牌均价环比(%)": -0.35,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 4917,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 79483
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 175,
            "成交均价环比(%)": 2.76,
            "月度": "2024年12月",
            "挂牌均价(元/m²)": 80577,
            "新增挂牌面积(m²)": 13939,
            "成交套数(套)": 72,
            "挂牌均价环比(%)": -1.81,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 5804,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 81676
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 90,
            "成交均价环比(%)": -2.23,
            "月度": "2025年01月",
            "挂牌均价(元/m²)": 77387,
            "新增挂牌面积(m²)": 7322,
            "成交套数(套)": 34,
            "挂牌均价环比(%)": -3.96,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 2889,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 79855
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 217,
            "成交均价环比(%)": -12.49,
            "月度": "2025年02月",
            "挂牌均价(元/m²)": 80282,
            "新增挂牌面积(m²)": 18538,
            "成交套数(套)": 22,
            "挂牌均价环比(%)": 3.74,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 1402,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 69882
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 226,
            "成交均价环比(%)": 7.29,
            "月度": "2025年03月",
            "挂牌均价(元/m²)": 81956,
            "新增挂牌面积(m²)": 19118,
            "成交套数(套)": 82,
            "挂牌均价环比(%)": 2.09,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 6573,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 74976
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 173,
            "成交均价环比(%)": -7.37,
            "月度": "2025年04月",
            "挂牌均价(元/m²)": 78560,
            "新增挂牌面积(m²)": 14109,
            "成交套数(套)": 49,
            "挂牌均价环比(%)": -4.14,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 3349,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 69449
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 190,
            "成交均价环比(%)": 2.89,
            "月度": "2025年05月",
            "挂牌均价(元/m²)": 79206,
            "新增挂牌面积(m²)": 15946,
            "成交套数(套)": 50,
            "挂牌均价环比(%)": 0.82,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 3688,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 71457
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 172,
            "成交均价环比(%)": 4.39,
            "月度": "2025年06月",
            "挂牌均价(元/m²)": 78951,
            "新增挂牌面积(m²)": 15655,
            "成交套数(套)": 30,
            "挂牌均价环比(%)": -0.32,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 2369,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 74596
          },
          {
            "项目所在板块": "凉城",
            "新增挂牌套数(套)": 108,
            "成交均价环比(%)": 0,
            "月度": "2025年07月",
            "挂牌均价(元/m²)": 76071,
            "新增挂牌面积(m²)": 10025,
            "成交套数(套)": 4,
            "挂牌均价环比(%)": 0,
            "挂牌所在板块": "大宁板块",
            "成交面积(m²)": 356,
            "交易所在板块": "大宁",
            "成交均价(元/m²)": 60253
          }
        ]
      }
    }
}

```
